<?php

namespace App\Console\Commands;

use App\Models\Part;
use App\Models\MobileModel;
use App\Models\UserFavorite;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class CleanModelsPartsDataCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'catalog:clean-all 
                            {--force : Skip confirmation prompts}
                            {--dry-run : Show what would be deleted without actually deleting}
                            {--models-only : Clean only models data}
                            {--parts-only : Clean only parts data}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean all models and parts data and reset auto-increment to start from 1';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧹 Models & Parts Complete Cleanup Tool');
        $this->info('=========================================');

        // Get current data counts
        $stats = $this->getCurrentStats();
        
        $this->displayCurrentStats($stats);

        if ($stats['total'] === 0) {
            $this->info('✅ No models or parts data found. Nothing to clean.');
            return 0;
        }

        if ($this->option('dry-run')) {
            $this->warn('🔍 DRY RUN MODE - No data will be deleted');
            $this->displayWhatWillBeDeleted($stats);
            return 0;
        }

        // Confirmation
        if (!$this->option('force')) {
            if (!$this->confirmDeletion($stats)) {
                $this->info('❌ Operation cancelled.');
                return 1;
            }
        }

        // Perform cleanup
        return $this->performCleanup($stats);
    }

    /**
     * Get current statistics of models and parts data.
     */
    private function getCurrentStats(): array
    {
        $modelsCount = $this->option('parts-only') ? 0 : MobileModel::count();
        $partsCount = $this->option('models-only') ? 0 : Part::count();
        $modelPartsCount = DB::table('model_parts')->count();
        $userFavoritesModelsCount = UserFavorite::where('favoritable_type', 'App\Models\MobileModel')->count();
        $userFavoritesPartsCount = UserFavorite::where('favoritable_type', 'App\Models\Part')->count();
        
        $modelsAutoIncrement = $this->getCurrentAutoIncrement('models');
        $partsAutoIncrement = $this->getCurrentAutoIncrement('parts');

        return [
            'models' => $modelsCount,
            'parts' => $partsCount,
            'model_parts' => $modelPartsCount,
            'user_favorites_models' => $userFavoritesModelsCount,
            'user_favorites_parts' => $userFavoritesPartsCount,
            'total' => $modelsCount + $partsCount + $modelPartsCount + $userFavoritesModelsCount + $userFavoritesPartsCount,
            'models_auto_increment' => $modelsAutoIncrement,
            'parts_auto_increment' => $partsAutoIncrement,
        ];
    }

    /**
     * Get current AUTO_INCREMENT value for specified table.
     */
    private function getCurrentAutoIncrement(string $table): int
    {
        $driver = DB::getDriverName();
        
        if ($driver === 'mysql') {
            $result = DB::select("SELECT AUTO_INCREMENT FROM information_schema.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '{$table}'");
            return $result[0]->AUTO_INCREMENT ?? 1;
        } elseif ($driver === 'sqlite') {
            $result = DB::select("SELECT seq FROM sqlite_sequence WHERE name = '{$table}'");
            return isset($result[0]) ? $result[0]->seq + 1 : 1;
        } else {
            return 1;
        }
    }

    /**
     * Display current statistics.
     */
    private function displayCurrentStats(array $stats): void
    {
        $this->info('📊 Current Data Statistics:');
        
        $tableData = [];
        
        if (!$this->option('parts-only')) {
            $tableData[] = ['Models', $stats['models']];
            $tableData[] = ['Models AUTO_INCREMENT', $stats['models_auto_increment']];
        }
        
        if (!$this->option('models-only')) {
            $tableData[] = ['Parts', $stats['parts']];
            $tableData[] = ['Parts AUTO_INCREMENT', $stats['parts_auto_increment']];
        }
        
        $tableData[] = ['Model-Part Relationships', $stats['model_parts']];
        $tableData[] = ['User Favorites (Models)', $stats['user_favorites_models']];
        $tableData[] = ['User Favorites (Parts)', $stats['user_favorites_parts']];
        $tableData[] = ['Total Records to Delete', $stats['total']];

        $this->table(['Data Type', 'Count'], $tableData);
    }

    /**
     * Display what will be deleted in dry-run mode.
     */
    private function displayWhatWillBeDeleted(array $stats): void
    {
        $this->info('🗑️  The following data would be deleted:');
        
        if ($stats['user_favorites_models'] > 0) {
            $this->line("   • {$stats['user_favorites_models']} user favorite records (models)");
        }
        
        if ($stats['user_favorites_parts'] > 0) {
            $this->line("   • {$stats['user_favorites_parts']} user favorite records (parts)");
        }
        
        if ($stats['model_parts'] > 0) {
            $this->line("   • {$stats['model_parts']} model-part relationship records");
        }
        
        if ($stats['parts'] > 0 && !$this->option('models-only')) {
            $this->line("   • {$stats['parts']} parts records");
            $this->line("   • Parts table AUTO_INCREMENT will be reset from {$stats['parts_auto_increment']} to 1");
        }
        
        if ($stats['models'] > 0 && !$this->option('parts-only')) {
            $this->line("   • {$stats['models']} models records");
            $this->line("   • Models table AUTO_INCREMENT will be reset from {$stats['models_auto_increment']} to 1");
        }
    }

    /**
     * Confirm deletion with user.
     */
    private function confirmDeletion(array $stats): bool
    {
        $this->warn('⚠️  WARNING: This action will permanently delete ALL selected data!');
        $this->displayWhatWillBeDeleted($stats);
        
        return $this->confirm('Are you sure you want to proceed?');
    }

    /**
     * Perform the actual cleanup operation.
     */
    private function performCleanup(array $stats): int
    {
        $this->info('🚀 Starting cleanup process...');

        try {
            $deletedCounts = [
                'user_favorites_models' => 0,
                'user_favorites_parts' => 0,
                'model_parts' => 0,
                'parts' => 0,
                'models' => 0,
            ];

            // Use transaction for data deletion only
            DB::beginTransaction();

            // Step 1: Delete user favorites for models
            if ($stats['user_favorites_models'] > 0) {
                $this->info('🗑️  Deleting user favorites (models)...');
                $deletedCounts['user_favorites_models'] = UserFavorite::where('favoritable_type', 'App\Models\MobileModel')->delete();
                $this->line("   ✅ Deleted {$deletedCounts['user_favorites_models']} user favorite records (models)");
            }

            // Step 2: Delete user favorites for parts
            if ($stats['user_favorites_parts'] > 0) {
                $this->info('🗑️  Deleting user favorites (parts)...');
                $deletedCounts['user_favorites_parts'] = UserFavorite::where('favoritable_type', 'App\Models\Part')->delete();
                $this->line("   ✅ Deleted {$deletedCounts['user_favorites_parts']} user favorite records (parts)");
            }

            // Step 3: Delete model-part relationships
            if ($stats['model_parts'] > 0) {
                $this->info('🗑️  Deleting model-part relationships...');
                $deletedCounts['model_parts'] = DB::table('model_parts')->delete();
                $this->line("   ✅ Deleted {$deletedCounts['model_parts']} model-part relationship records");
            }

            // Step 4: Delete parts (if not models-only)
            if ($stats['parts'] > 0 && !$this->option('models-only')) {
                $this->info('🗑️  Deleting parts...');
                $deletedCounts['parts'] = Part::query()->delete();
                $this->line("   ✅ Deleted {$deletedCounts['parts']} parts records");
            }

            // Step 5: Delete models (if not parts-only)
            if ($stats['models'] > 0 && !$this->option('parts-only')) {
                $this->info('🗑️  Deleting models...');
                $deletedCounts['models'] = MobileModel::query()->delete();
                $this->line("   ✅ Deleted {$deletedCounts['models']} models records");
            }

            DB::commit();

            // Step 6: Reset AUTO_INCREMENT values (outside transaction)
            $this->info('🔄 Resetting AUTO_INCREMENT values...');
            
            if (!$this->option('models-only')) {
                $this->resetAutoIncrement('parts');
                $this->line('   ✅ Parts table AUTO_INCREMENT reset to 1');
            }
            
            if (!$this->option('parts-only')) {
                $this->resetAutoIncrement('models');
                $this->line('   ✅ Models table AUTO_INCREMENT reset to 1');
            }

            $this->displayCleanupSummary($deletedCounts, $stats);
            
            return 0;

        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('❌ Cleanup failed: ' . $e->getMessage());
            $this->error('🔄 All changes have been rolled back.');
            return 1;
        }
    }

    /**
     * Reset AUTO_INCREMENT for specified table based on database driver.
     */
    private function resetAutoIncrement(string $table): void
    {
        $driver = DB::getDriverName();
        
        if ($driver === 'mysql') {
            DB::statement("ALTER TABLE {$table} AUTO_INCREMENT = 1");
        } elseif ($driver === 'sqlite') {
            DB::statement("DELETE FROM sqlite_sequence WHERE name = '{$table}'");
        } else {
            try {
                DB::statement("ALTER TABLE {$table} AUTO_INCREMENT = 1");
            } catch (\Exception $e) {
                $this->warn("Could not reset AUTO_INCREMENT for {$table} on this database type");
            }
        }
    }

    /**
     * Display cleanup summary.
     */
    private function displayCleanupSummary(array $deletedCounts, array $originalStats): void
    {
        $this->info('');
        $this->info('✅ Cleanup completed successfully!');
        $this->info('================================');
        
        $summaryData = [];
        
        if ($deletedCounts['user_favorites_models'] > 0) {
            $summaryData[] = ['User Favorites (Models)', $deletedCounts['user_favorites_models']];
        }
        
        if ($deletedCounts['user_favorites_parts'] > 0) {
            $summaryData[] = ['User Favorites (Parts)', $deletedCounts['user_favorites_parts']];
        }
        
        if ($deletedCounts['model_parts'] > 0) {
            $summaryData[] = ['Model-Part Relationships', $deletedCounts['model_parts']];
        }
        
        if ($deletedCounts['parts'] > 0) {
            $summaryData[] = ['Parts', $deletedCounts['parts']];
        }
        
        if ($deletedCounts['models'] > 0) {
            $summaryData[] = ['Models', $deletedCounts['models']];
        }
        
        $summaryData[] = ['Total', array_sum($deletedCounts)];

        $this->table(['Operation', 'Records Deleted'], $summaryData);

        if (!$this->option('models-only')) {
            $newPartsAutoIncrement = $this->getCurrentAutoIncrement('parts');
            $this->info("🔄 Parts AUTO_INCREMENT: {$originalStats['parts_auto_increment']} → {$newPartsAutoIncrement}");
        }
        
        if (!$this->option('parts-only')) {
            $newModelsAutoIncrement = $this->getCurrentAutoIncrement('models');
            $this->info("🔄 Models AUTO_INCREMENT: {$originalStats['models_auto_increment']} → {$newModelsAutoIncrement}");
        }
        
        $this->info('');
        $this->info('🎉 Next models and parts created will have ID: 1');
        $this->info('💡 You can now recreate models and parts that will start indexing from 1.');
    }
}
