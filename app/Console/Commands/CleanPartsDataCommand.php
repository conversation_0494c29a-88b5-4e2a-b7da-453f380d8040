<?php

namespace App\Console\Commands;

use App\Models\Part;
use App\Models\UserFavorite;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CleanPartsDataCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'parts:clean-all 
                            {--force : Skip confirmation prompts}
                            {--dry-run : Show what would be deleted without actually deleting}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean all parts-related data and reset auto-increment to start from 1';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧹 Parts Data Cleanup Tool');
        $this->info('==========================');

        // Get current data counts
        $stats = $this->getCurrentStats();
        
        $this->displayCurrentStats($stats);

        if ($stats['total'] === 0) {
            $this->info('✅ No parts-related data found. Nothing to clean.');
            return 0;
        }

        if ($this->option('dry-run')) {
            $this->warn('🔍 DRY RUN MODE - No data will be deleted');
            $this->displayWhatWillBeDeleted($stats);
            return 0;
        }

        // Confirmation
        if (!$this->option('force')) {
            if (!$this->confirmDeletion($stats)) {
                $this->info('❌ Operation cancelled.');
                return 1;
            }
        }

        // Perform cleanup
        return $this->performCleanup($stats);
    }

    /**
     * Get current statistics of parts-related data.
     */
    private function getCurrentStats(): array
    {
        $partsCount = Part::count();
        $modelPartsCount = DB::table('model_parts')->count();
        $userFavoritesCount = UserFavorite::where('favoritable_type', 'App\Models\Part')->count();
        $currentAutoIncrement = $this->getCurrentAutoIncrement();

        return [
            'parts' => $partsCount,
            'model_parts' => $modelPartsCount,
            'user_favorites' => $userFavoritesCount,
            'total' => $partsCount + $modelPartsCount + $userFavoritesCount,
            'auto_increment' => $currentAutoIncrement,
        ];
    }

    /**
     * Get current AUTO_INCREMENT value for parts table.
     */
    private function getCurrentAutoIncrement(): int
    {
        $driver = DB::getDriverName();

        if ($driver === 'mysql') {
            $result = DB::select('SELECT AUTO_INCREMENT FROM information_schema.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = "parts"');
            return $result[0]->AUTO_INCREMENT ?? 1;
        } elseif ($driver === 'sqlite') {
            // For SQLite, get the next sequence value
            $result = DB::select('SELECT seq FROM sqlite_sequence WHERE name = "parts"');
            return isset($result[0]) ? $result[0]->seq + 1 : 1;
        } else {
            // For other databases, return a default value
            return 1;
        }
    }

    /**
     * Display current statistics.
     */
    private function displayCurrentStats(array $stats): void
    {
        $this->info('📊 Current Data Statistics:');
        $this->table(
            ['Data Type', 'Count'],
            [
                ['Parts', $stats['parts']],
                ['Model-Part Relationships', $stats['model_parts']],
                ['User Favorites (Parts)', $stats['user_favorites']],
                ['Current AUTO_INCREMENT', $stats['auto_increment']],
                ['Total Records to Delete', $stats['total']],
            ]
        );
    }

    /**
     * Display what will be deleted in dry-run mode.
     */
    private function displayWhatWillBeDeleted(array $stats): void
    {
        $this->info('🗑️  The following data would be deleted:');
        
        if ($stats['parts'] > 0) {
            $this->line("   • {$stats['parts']} parts records");
        }
        
        if ($stats['model_parts'] > 0) {
            $this->line("   • {$stats['model_parts']} model-part relationship records");
        }
        
        if ($stats['user_favorites'] > 0) {
            $this->line("   • {$stats['user_favorites']} user favorite records");
        }

        $this->line("   • Parts table AUTO_INCREMENT will be reset from {$stats['auto_increment']} to 1");
    }

    /**
     * Confirm deletion with user.
     */
    private function confirmDeletion(array $stats): bool
    {
        $this->warn('⚠️  WARNING: This action will permanently delete ALL parts-related data!');
        $this->displayWhatWillBeDeleted($stats);
        
        return $this->confirm('Are you sure you want to proceed?');
    }

    /**
     * Perform the actual cleanup operation.
     */
    private function performCleanup(array $stats): int
    {
        $this->info('🚀 Starting cleanup process...');

        try {
            $deletedCounts = [
                'user_favorites' => 0,
                'model_parts' => 0,
                'parts' => 0,
            ];

            // Use transaction for data deletion only
            DB::beginTransaction();

            // Step 1: Delete user favorites referencing parts
            if ($stats['user_favorites'] > 0) {
                $this->info('🗑️  Deleting user favorites...');
                $deletedCounts['user_favorites'] = UserFavorite::where('favoritable_type', 'App\Models\Part')->delete();
                $this->line("   ✅ Deleted {$deletedCounts['user_favorites']} user favorite records");
            }

            // Step 2: Delete model-part relationships (CASCADE will handle this, but explicit is better)
            if ($stats['model_parts'] > 0) {
                $this->info('🗑️  Deleting model-part relationships...');
                $deletedCounts['model_parts'] = DB::table('model_parts')->delete();
                $this->line("   ✅ Deleted {$deletedCounts['model_parts']} model-part relationship records");
            }

            // Step 3: Delete parts
            if ($stats['parts'] > 0) {
                $this->info('🗑️  Deleting parts...');
                $deletedCounts['parts'] = Part::query()->delete();
                $this->line("   ✅ Deleted {$deletedCounts['parts']} parts records");
            }

            DB::commit();

            // Step 4: Reset AUTO_INCREMENT (outside transaction as DDL can cause implicit commits)
            $this->info('🔄 Resetting AUTO_INCREMENT...');
            $this->resetAutoIncrement();
            $this->line('   ✅ Parts table AUTO_INCREMENT reset to 1');

            $this->displayCleanupSummary($deletedCounts, $stats);
            
            return 0;

        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('❌ Cleanup failed: ' . $e->getMessage());
            $this->error('🔄 All changes have been rolled back.');
            return 1;
        }
    }

    /**
     * Display cleanup summary.
     */
    private function displayCleanupSummary(array $deletedCounts, array $originalStats): void
    {
        $this->info('');
        $this->info('✅ Cleanup completed successfully!');
        $this->info('================================');
        
        $this->table(
            ['Operation', 'Records Deleted'],
            [
                ['User Favorites', $deletedCounts['user_favorites']],
                ['Model-Part Relationships', $deletedCounts['model_parts']],
                ['Parts', $deletedCounts['parts']],
                ['Total', array_sum($deletedCounts)],
            ]
        );

        $newAutoIncrement = $this->getCurrentAutoIncrement();
        $this->info("🔄 AUTO_INCREMENT: {$originalStats['auto_increment']} → {$newAutoIncrement}");
        
        $this->info('');
        $this->info('🎉 Next part created will have ID: 1');
        $this->info('💡 You can now create new parts that will start indexing from 1.');
    }

    /**
     * Reset AUTO_INCREMENT for parts table based on database driver.
     */
    private function resetAutoIncrement(): void
    {
        $driver = DB::getDriverName();

        if ($driver === 'mysql') {
            DB::statement('ALTER TABLE parts AUTO_INCREMENT = 1');
        } elseif ($driver === 'sqlite') {
            // For SQLite, delete the sequence entry to reset it
            DB::statement('DELETE FROM sqlite_sequence WHERE name = "parts"');
        } else {
            // For other databases, attempt a generic approach
            try {
                DB::statement('ALTER TABLE parts AUTO_INCREMENT = 1');
            } catch (\Exception $e) {
                // If it fails, just continue - the important part is data cleanup
                $this->warn('Could not reset AUTO_INCREMENT for this database type');
            }
        }
    }
}
