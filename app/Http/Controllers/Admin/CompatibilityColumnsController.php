<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\CompatibilityColumnService;
use Illuminate\Http\Request;

class CompatibilityColumnsController extends Controller
{
    private CompatibilityColumnService $columnService;

    public function __construct(CompatibilityColumnService $columnService)
    {
        $this->columnService = $columnService;
    }

    /**
     * Get column configuration.
     */
    public function getConfig()
    {
        $config = $this->columnService->getColumnConfiguration(true);

        return response()->json([
            'success' => true,
            'data' => $config
        ]);
    }

    /**
     * Update column configuration.
     */
    public function updateConfig(Request $request)
    {
        $validated = $request->validate([
            'columns' => 'required|array',
            'columns.*.enabled' => 'required|boolean',
            'columns.*.required' => 'required|boolean',
            'columns.*.order' => 'required|integer|min:1',
            'columns.*.label' => 'required|string|max:255',
            'columns.*.source' => 'required|string|max:255',
            'columns.*.priority' => 'required|integer|min:1',
            'columns.*.minBreakpoint' => 'required|string|in:xs,sm,md,lg,xl'
        ]);

        $result = $this->columnService->updateColumnConfiguration($validated['columns']);

        if ($result) {
            return response()->json([
                'success' => true,
                'message' => 'Column configuration updated successfully',
                'data' => $validated['columns']
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Failed to update column configuration'
        ], 500);
    }

    /**
     * Toggle a single column.
     */
    public function toggleColumn(Request $request)
    {
        $validated = $request->validate([
            'column' => 'required|string'
        ]);

        $config = $this->columnService->getColumnConfiguration(true);
        $columnKey = $validated['column'];

        if (!isset($config[$columnKey])) {
            return response()->json([
                'success' => false,
                'message' => 'Column not found'
            ], 422);
        }

        $column = $config[$columnKey];

        // Don't allow disabling required columns
        if ($column['required'] && $column['enabled']) {
            return response()->json([
                'success' => false,
                'message' => 'Required columns cannot be disabled'
            ], 422);
        }

        // Toggle the column
        $config[$columnKey]['enabled'] = !$column['enabled'];

        $result = $this->columnService->updateColumnConfiguration($config);

        if ($result) {
            return response()->json([
                'success' => true,
                'message' => 'Column toggled successfully',
                'data' => $config
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Failed to toggle column'
        ], 500);
    }

    /**
     * Validate column configuration.
     */
    public function validateConfig(Request $request)
    {
        $validated = $request->validate([
            'columns' => 'required|array',
            'columns.*.enabled' => 'required|boolean',
            'columns.*.required' => 'required|boolean',
            'columns.*.order' => 'required|integer|min:1',
            'columns.*.label' => 'required|string|max:255',
            'columns.*.source' => 'required|string|max:255',
            'columns.*.priority' => 'required|integer|min:1',
            'columns.*.minBreakpoint' => 'required|string|in:xs,sm,md,lg,xl'
        ]);

        $errors = $this->columnService->validateConfiguration($validated['columns']);

        return response()->json([
            'success' => empty($errors),
            'errors' => $errors
        ]);
    }
}
