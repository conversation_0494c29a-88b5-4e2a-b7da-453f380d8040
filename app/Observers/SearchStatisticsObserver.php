<?php

namespace App\Observers;

use App\Services\SearchStatisticsService;
use Illuminate\Support\Facades\App;

class SearchStatisticsObserver
{
    /**
     * Clear search statistics cache when relevant data changes.
     */
    public function clearCache(): void
    {
        try {
            $statisticsService = App::make(SearchStatisticsService::class);
            $statisticsService->clearCache();
        } catch (\Exception $e) {
            // Log error but don't fail the operation
            \Log::warning('Failed to clear search statistics cache: ' . $e->getMessage());
        }
    }

    /**
     * Handle the model "created" event.
     */
    public function created($model): void
    {
        $this->clearCache();
    }

    /**
     * Handle the model "updated" event.
     */
    public function updated($model): void
    {
        $this->clearCache();
    }

    /**
     * Handle the model "deleted" event.
     */
    public function deleted($model): void
    {
        $this->clearCache();
    }

    /**
     * Handle the model "restored" event.
     */
    public function restored($model): void
    {
        $this->clearCache();
    }

    /**
     * Handle the model "force deleted" event.
     */
    public function forceDeleted($model): void
    {
        $this->clearCache();
    }
}
