<?php

namespace App\Services;

use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class SearchStatisticsService
{
    /**
     * Cache duration in minutes (1 hour)
     */
    private const CACHE_DURATION = 60;

    /**
     * Get comprehensive search statistics.
     */
    public function getSearchStatistics(): array
    {
        return Cache::remember('search_statistics', self::CACHE_DURATION, function () {
            return [
                'parts_count' => $this->getPartsCount(),
                'models_count' => $this->getModelsCount(),
                'categories_count' => $this->getCategoriesCount(),
                'brands_count' => $this->getBrandsCount(),
                'additional_stats' => $this->getAdditionalStats(),
            ];
        });
    }

    /**
     * Get total count of active parts.
     */
    public function getPartsCount(): int
    {
        return Cache::remember('search_stats_parts_count', self::CACHE_DURATION, function () {
            return Part::active()->count();
        });
    }

    /**
     * Get total count of active mobile models.
     */
    public function getModelsCount(): int
    {
        return Cache::remember('search_stats_models_count', self::CACHE_DURATION, function () {
            return MobileModel::active()->count();
        });
    }

    /**
     * Get total count of active categories.
     */
    public function getCategoriesCount(): int
    {
        return Cache::remember('search_stats_categories_count', self::CACHE_DURATION, function () {
            return Category::active()->count();
        });
    }

    /**
     * Get total count of active brands.
     */
    public function getBrandsCount(): int
    {
        return Cache::remember('search_stats_brands_count', self::CACHE_DURATION, function () {
            return Brand::active()->count();
        });
    }

    /**
     * Get additional statistics for enhanced display.
     */
    public function getAdditionalStats(): array
    {
        return Cache::remember('search_stats_additional', self::CACHE_DURATION, function () {
            return [
                'compatible_parts_count' => $this->getCompatiblePartsCount(),
                'verified_parts_count' => $this->getVerifiedPartsCount(),
                'models_with_parts_count' => $this->getModelsWithPartsCount(),
                'categories_with_parts_count' => $this->getCategoriesWithPartsCount(),
            ];
        });
    }

    /**
     * Get count of parts that are marked as compatible with at least one model.
     */
    private function getCompatiblePartsCount(): int
    {
        return DB::table('model_parts')
            ->where('is_compatible', true)
            ->distinct('part_id')
            ->count('part_id');
    }

    /**
     * Get count of parts that are verified.
     */
    private function getVerifiedPartsCount(): int
    {
        return DB::table('model_parts')
            ->where('is_verified', true)
            ->distinct('part_id')
            ->count('part_id');
    }

    /**
     * Get count of models that have at least one part.
     */
    private function getModelsWithPartsCount(): int
    {
        return MobileModel::active()
            ->has('parts')
            ->count();
    }

    /**
     * Get count of categories that have at least one part.
     */
    private function getCategoriesWithPartsCount(): int
    {
        return Category::active()
            ->has('parts')
            ->count();
    }

    /**
     * Format count for display with appropriate suffix.
     */
    public function formatCount(int $count): string
    {
        if ($count >= 1000000) {
            return number_format($count / 1000000, 1) . 'M+';
        } elseif ($count >= 1000) {
            return number_format($count / 1000, 1) . 'K+';
        } else {
            return number_format($count) . '+';
        }
    }

    /**
     * Get formatted statistics ready for frontend display.
     */
    public function getFormattedStatistics(): array
    {
        $stats = $this->getSearchStatistics();

        return [
            'parts' => [
                'count' => $stats['parts_count'],
                'formatted' => $this->formatCount($stats['parts_count']),
                'label' => 'Parts Available',
            ],
            'models' => [
                'count' => $stats['models_count'],
                'formatted' => $this->formatCount($stats['models_count']),
                'label' => 'Mobile Models',
            ],
            'categories' => [
                'count' => $stats['categories_count'],
                'formatted' => $this->formatCount($stats['categories_count']),
                'label' => 'Categories',
            ],
            'brands' => [
                'count' => $stats['brands_count'],
                'formatted' => $this->formatCount($stats['brands_count']),
                'label' => 'Brands',
            ],
        ];
    }

    /**
     * Clear all statistics cache.
     */
    public function clearCache(): void
    {
        $cacheKeys = [
            'search_statistics',
            'search_stats_parts_count',
            'search_stats_models_count',
            'search_stats_categories_count',
            'search_stats_brands_count',
            'search_stats_additional',
        ];

        foreach ($cacheKeys as $key) {
            Cache::forget($key);
        }
    }

    /**
     * Refresh statistics cache.
     */
    public function refreshCache(): array
    {
        $this->clearCache();
        return $this->getSearchStatistics();
    }
}
