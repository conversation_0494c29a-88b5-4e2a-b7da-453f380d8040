<?php

namespace App\Traits;

trait CsvDataTrimmer
{
    /**
     * Trim leading and trailing spaces from a value while preserving internal spaces.
     * Handles various types of whitespace including non-breaking spaces.
     *
     * @param mixed $value The value to trim
     * @return mixed The trimmed value (null, empty string, or trimmed string)
     */
    protected function trimCsvValue($value)
    {
        // Return null and empty strings as-is
        if ($value === null || $value === '') {
            return $value;
        }

        // Convert to string
        $stringValue = (string) $value;

        // Trim leading/trailing whitespace including non-breaking spaces and other Unicode whitespace
        $trimmed = preg_replace('/^[\s\x{00A0}\x{2000}-\x{200B}\x{2028}\x{2029}\x{202F}\x{205F}\x{3000}]+|[\s\x{00A0}\x{2000}-\x{200B}\x{2028}\x{2029}\x{202F}\x{205F}\x{3000}]+$/u', '', $stringValue);

        // Also apply standard trim as fallback
        $trimmed = trim($trimmed);

        return $trimmed;
    }
    
    /**
     * Trim all string values in an array while preserving the array structure.
     * 
     * @param array $data The array of data to trim
     * @return array The array with all string values trimmed
     */
    protected function trimCsvData(array $data): array
    {
        $trimmed = [];
        
        foreach ($data as $key => $value) {
            $trimmed[$key] = $this->trimCsvValue($value);
        }
        
        return $trimmed;
    }
    
    /**
     * Trim specific fields in an array, leaving other fields unchanged.
     * 
     * @param array $data The array of data
     * @param array $fields The fields to trim
     * @return array The array with specified fields trimmed
     */
    protected function trimCsvFields(array $data, array $fields): array
    {
        foreach ($fields as $field) {
            if (array_key_exists($field, $data)) {
                $data[$field] = $this->trimCsvValue($data[$field]);
            }
        }
        
        return $data;
    }
    
    /**
     * Clean and normalize CSV header values by removing BOM, quotes, and whitespace.
     *
     * @param array $headers The array of header values
     * @return array The cleaned header values
     */
    protected function cleanCsvHeaders(array $headers): array
    {
        return array_map(function($header) {
            // Remove BOM, quotes (including backticks), and various whitespace characters
            $cleaned = trim($header, " \t\n\r\0\x0B\x0C\xEF\xBB\xBF\"'`");
            // Remove any remaining invisible characters
            $cleaned = preg_replace('/[\x00-\x1F\x7F]/', '', $cleaned);
            // Apply additional trimming for Unicode whitespace
            $cleaned = $this->trimCsvValue($cleaned);
            return $cleaned;
        }, $headers);
    }

    /**
     * Normalize a string for duplicate detection by removing all types of whitespace
     * and converting to lowercase for case-insensitive comparison.
     *
     * @param string $value The value to normalize
     * @return string The normalized value
     */
    protected function normalizeForDuplicateDetection(string $value): string
    {
        // First trim the value
        $normalized = $this->trimCsvValue($value);

        // Replace all Unicode whitespace characters with regular spaces
        $normalized = preg_replace('/[\s\x{00A0}\x{2000}-\x{200B}\x{2028}\x{2029}\x{202F}\x{205F}\x{3000}]+/u', ' ', $normalized);

        // Remove any remaining internal multiple spaces and replace with single space
        $normalized = preg_replace('/\s+/', ' ', $normalized);

        // Trim again to remove any edge spaces that might have been created
        $normalized = trim($normalized);

        // Convert to lowercase for case-insensitive comparison
        $normalized = mb_strtolower($normalized, 'UTF-8');

        return $normalized;
    }

    /**
     * Enhanced CSV data parsing that handles quoted fields with better whitespace management.
     *
     * @param string $line The CSV line to parse
     * @param string $delimiter The field delimiter (default: ',')
     * @param string $enclosure The field enclosure (default: '"')
     * @param string $escape The escape character (default: '\\')
     * @return array The parsed CSV fields
     */
    protected function parseEnhancedCsvLine(string $line, string $delimiter = ',', string $enclosure = '"', string $escape = '\\'): array
    {
        // Use str_getcsv for initial parsing
        $fields = str_getcsv($line, $delimiter, $enclosure, $escape);

        // Apply trimming to each field
        return array_map([$this, 'trimCsvValue'], $fields);
    }
}
