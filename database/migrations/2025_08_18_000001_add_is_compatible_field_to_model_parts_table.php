<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('model_parts', function (Blueprint $table) {
            // Add is_compatible field after is_verified
            // Default to true to maintain existing functionality
            $table->boolean('is_compatible')->default(true)->after('is_verified');
            
            // Add index for better query performance
            $table->index('is_compatible');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('model_parts', function (Blueprint $table) {
            // Drop index first
            $table->dropIndex(['is_compatible']);
            
            // Drop the column
            $table->dropColumn('is_compatible');
        });
    }
};
