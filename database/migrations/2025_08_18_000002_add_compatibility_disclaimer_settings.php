<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\SiteSetting;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add compatibility disclaimer settings
        SiteSetting::create([
            'key' => 'parts_compatibility_disclaimer_enabled',
            'value' => true,
            'type' => 'boolean',
            'description' => 'Enable compatibility disclaimer on public part details pages',
            'category' => 'parts_management',
            'is_active' => true,
        ]);

        SiteSetting::create([
            'key' => 'parts_compatibility_disclaimer_text',
            'value' => 'Compatibility information is provided for reference only. Please verify compatibility before making a purchase.',
            'type' => 'text',
            'description' => 'Disclaimer text shown on public part details pages',
            'category' => 'parts_management',
            'is_active' => true,
        ]);

        SiteSetting::create([
            'key' => 'parts_compatibility_disclaimer_article_title',
            'value' => 'Read our compatibility guide',
            'type' => 'string',
            'description' => 'Title for the disclaimer article link button',
            'category' => 'parts_management',
            'is_active' => true,
        ]);

        SiteSetting::create([
            'key' => 'parts_compatibility_disclaimer_article_url',
            'value' => '',
            'type' => 'string',
            'description' => 'URL to the compatibility guide article (leave empty to hide the link)',
            'category' => 'parts_management',
            'is_active' => true,
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove the compatibility disclaimer settings
        SiteSetting::whereIn('key', [
            'parts_compatibility_disclaimer_enabled',
            'parts_compatibility_disclaimer_text',
            'parts_compatibility_disclaimer_article_title',
            'parts_compatibility_disclaimer_article_url',
        ])->delete();
    }
};
