<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Clean whitespace from brand names
        $this->cleanBrandNames();
        
        // Clean whitespace from model names
        $this->cleanModelNames();
        
        // Remove duplicate models that may have been created due to spacing issues
        $this->removeDuplicateModels();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration cannot be reversed as it cleans data
        // The original data with spacing issues would be lost
    }

    /**
     * Clean whitespace from brand names.
     */
    private function cleanBrandNames(): void
    {
        $brands = DB::table('brands')->get();
        
        foreach ($brands as $brand) {
            $cleanedName = $this->trimValue($brand->name);
            $cleanedCountry = $this->trimValue($brand->country);
            $cleanedWebsite = $this->trimValue($brand->website);
            $cleanedLogoUrl = $this->trimValue($brand->logo_url);
            
            // Only update if there are changes
            if ($cleanedName !== $brand->name || 
                $cleanedCountry !== $brand->country || 
                $cleanedWebsite !== $brand->website || 
                $cleanedLogoUrl !== $brand->logo_url) {
                
                DB::table('brands')
                    ->where('id', $brand->id)
                    ->update([
                        'name' => $cleanedName,
                        'country' => $cleanedCountry,
                        'website' => $cleanedWebsite,
                        'logo_url' => $cleanedLogoUrl,
                        'updated_at' => now(),
                    ]);
            }
        }
    }

    /**
     * Clean whitespace from model names.
     */
    private function cleanModelNames(): void
    {
        $models = DB::table('models')->get();
        
        foreach ($models as $model) {
            $cleanedName = $this->trimValue($model->name);
            $cleanedModelNumber = $this->trimValue($model->model_number);
            $cleanedImageUrl = $this->trimValue($model->image_url);
            
            // Only update if there are changes
            if ($cleanedName !== $model->name || 
                $cleanedModelNumber !== $model->model_number || 
                $cleanedImageUrl !== $model->image_url) {
                
                DB::table('models')
                    ->where('id', $model->id)
                    ->update([
                        'name' => $cleanedName,
                        'model_number' => $cleanedModelNumber,
                        'image_url' => $cleanedImageUrl,
                        'updated_at' => now(),
                    ]);
            }
        }
    }

    /**
     * Remove duplicate models that may have been created due to spacing issues.
     */
    private function removeDuplicateModels(): void
    {
        // Get all models grouped by brand_id and normalized name
        $models = DB::table('models')
            ->select('id', 'brand_id', 'name', 'created_at')
            ->orderBy('brand_id')
            ->orderBy('name')
            ->orderBy('created_at')
            ->get();

        $seenModels = [];
        $duplicateIds = [];

        foreach ($models as $model) {
            $normalizedName = $this->normalizeForComparison($model->name);
            $key = $model->brand_id . '|' . $normalizedName;

            if (isset($seenModels[$key])) {
                // This is a duplicate, mark for deletion (keep the older one)
                $duplicateIds[] = $model->id;
            } else {
                $seenModels[$key] = $model->id;
            }
        }

        // Delete duplicate models
        if (!empty($duplicateIds)) {
            DB::table('models')->whereIn('id', $duplicateIds)->delete();
        }
    }

    /**
     * Trim whitespace from a value.
     */
    private function trimValue(?string $value): ?string
    {
        if ($value === null || $value === '') {
            return $value;
        }

        // Trim various types of whitespace including non-breaking spaces
        $trimmed = preg_replace('/^[\s\x{00A0}\x{2000}-\x{200B}\x{2028}\x{2029}\x{202F}\x{205F}\x{3000}]+|[\s\x{00A0}\x{2000}-\x{200B}\x{2028}\x{2029}\x{202F}\x{205F}\x{3000}]+$/u', '', $value);
        
        return trim($trimmed);
    }

    /**
     * Normalize a string for comparison.
     */
    private function normalizeForComparison(string $value): string
    {
        $normalized = $this->trimValue($value);
        $normalized = preg_replace('/\s+/', ' ', $normalized);
        return mb_strtolower($normalized, 'UTF-8');
    }
};
