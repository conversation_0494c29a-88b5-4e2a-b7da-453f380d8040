<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\SiteSetting;
use App\Services\CompatibilityColumnService;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Initialize the compatibility columns configuration with proper defaults
        $columnService = app(CompatibilityColumnService::class);

        // Get the default configuration using reflection
        $reflection = new ReflectionClass($columnService);
        $method = $reflection->getMethod('getDefaultConfiguration');
        $method->setAccessible(true);
        $defaultConfig = $method->invoke($columnService);

        // Enable essential columns for a useful export
        $essentialColumns = [
            'brand', 'model', 'model_number', 'part_name', 'part_number',
            'category', 'display_type', 'display_size', 'location',
            'compatible', 'notes', 'verified'
        ];

        foreach ($essentialColumns as $columnKey) {
            if (isset($defaultConfig[$columnKey])) {
                $defaultConfig[$columnKey]['enabled'] = true;
            }
        }

        // Update the configuration in the database
        SiteSetting::set(
            'parts_compatibility_columns',
            $defaultConfig,
            'array',
            'Configuration for parts compatibility table columns',
            'parts_management'
        );
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove the configuration
        SiteSetting::where('key', 'parts_compatibility_columns')->delete();
    }
};
