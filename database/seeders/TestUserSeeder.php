<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class TestUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create a free test user
        $freeUser = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Free Test User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
                'subscription_plan' => 'free',
                'status' => 'active',
                'approval_status' => 'approved',
            ]
        );

        // Create a premium test user for comparison
        $premiumUser = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Premium Test User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
                'subscription_plan' => 'premium',
                'status' => 'active',
                'approval_status' => 'approved',
            ]
        );

        // Create active subscription for premium user
        if ($premiumUser) {
            // First, try to find or create a premium pricing plan
            $premiumPlan = \App\Models\PricingPlan::firstOrCreate(
                ['name' => 'premium'],
                [
                    'display_name' => 'Premium',
                    'description' => 'Premium subscription plan',
                    'price' => 19.00,
                    'currency' => 'USD',
                    'interval' => 'month',
                    'features' => ['Unlimited searches', 'Access to favorites', 'Ad-free experience'],
                    'is_active' => true,
                    'is_public' => true,
                ]
            );

            // Create active subscription
            \App\Models\Subscription::updateOrCreate(
                ['user_id' => $premiumUser->id],
                [
                    'user_id' => $premiumUser->id,
                    'pricing_plan_id' => $premiumPlan->id,
                    'plan_name' => 'premium',
                    'status' => 'active',
                    'current_period_start' => now()->subDays(5),
                    'current_period_end' => now()->addDays(25),
                    'payment_gateway' => 'test',
                ]
            );
        }

        $this->command->info('Test users created successfully:');
        $this->command->info('Free User: <EMAIL> / password');
        $this->command->info('Premium User: <EMAIL> / password');
    }
}
