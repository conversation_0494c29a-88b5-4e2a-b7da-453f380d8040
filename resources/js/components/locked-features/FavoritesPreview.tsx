import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Link } from '@inertiajs/react';
import { Eye, Heart, Lock, Search, Smartphone, Star } from 'lucide-react';

interface FavoritesPreviewProps {
    className?: string;
}

const mockFavorites = [
    {
        id: 1,
        name: "iPhone 14 Pro Max OLED Display",
        category: "Display",
        brand: "Apple",
        image: "/images/placeholder-part.jpg",
        price: "$299.99",
        rating: 4.8,
        inStock: true
    },
    {
        id: 2,
        name: "Samsung Galaxy S23 Ultra Battery",
        category: "Battery",
        brand: "Samsung",
        image: "/images/placeholder-part.jpg",
        price: "$89.99",
        rating: 4.6,
        inStock: true
    },
    {
        id: 3,
        name: "OnePlus 11 5G Camera Module",
        category: "Camera",
        brand: "OnePlus",
        image: "/images/placeholder-part.jpg",
        price: "$159.99",
        rating: 4.7,
        inStock: false
    },
    {
        id: 4,
        name: "Google Pixel 7 Pro Charging Port",
        category: "Charging",
        brand: "Google",
        image: "/images/placeholder-part.jpg",
        price: "$45.99",
        rating: 4.5,
        inStock: true
    }
];

export function FavoritesPreview({ className }: FavoritesPreviewProps) {
    return (
        <div className={`space-y-4 ${className}`}>
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h3 className="text-lg font-semibold text-foreground">Your Favorites Collection</h3>
                    <p className="text-sm text-muted-foreground">Quick access to your saved parts and models</p>
                </div>
                <Badge variant="secondary" className="bg-gradient-to-r from-amber-500 to-orange-500 text-white border-0">
                    Premium Feature
                </Badge>
            </div>

            {/* Favorites Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {mockFavorites.map((item) => (
                    <Card key={item.id} className="relative overflow-hidden border border-muted hover:border-primary/30 transition-colors">
                        {/* Lock Overlay */}
                        <div className="absolute inset-0 bg-background/80 backdrop-blur-sm z-10 flex items-center justify-center">
                            <div className="text-center">
                                <div className="w-12 h-12 bg-muted rounded-full flex items-center justify-center mx-auto mb-2">
                                    <Lock className="w-6 h-6 text-muted-foreground" />
                                </div>
                                <p className="text-sm font-medium text-muted-foreground">Premium Required</p>
                            </div>
                        </div>

                        <CardHeader className="pb-3">
                            <div className="flex items-start justify-between gap-2">
                                <div className="flex-1 min-w-0">
                                    <CardTitle className="text-sm font-medium text-foreground truncate">
                                        {item.name}
                                    </CardTitle>
                                    <CardDescription className="text-xs text-muted-foreground">
                                        {item.brand} • {item.category}
                                    </CardDescription>
                                </div>
                                <Heart className="w-4 h-4 text-red-500 fill-current flex-shrink-0" />
                            </div>
                        </CardHeader>

                        <CardContent className="pt-0">
                            <div className="space-y-3">
                                {/* Image Placeholder */}
                                <div className="w-full h-24 bg-muted rounded-lg flex items-center justify-center">
                                    <Smartphone className="w-8 h-8 text-muted-foreground" />
                                </div>

                                {/* Details */}
                                <div className="space-y-2">
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm font-semibold text-foreground">{item.price}</span>
                                        <div className="flex items-center gap-1">
                                            <Star className="w-3 h-3 text-yellow-500 fill-current" />
                                            <span className="text-xs text-muted-foreground">{item.rating}</span>
                                        </div>
                                    </div>
                                    
                                    <div className="flex items-center justify-between">
                                        <Badge 
                                            variant={item.inStock ? "default" : "secondary"}
                                            className="text-xs"
                                        >
                                            {item.inStock ? "In Stock" : "Out of Stock"}
                                        </Badge>
                                        <Button size="sm" variant="outline" disabled>
                                            <Eye className="w-3 h-3 mr-1" />
                                            View
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                ))}
            </div>

            {/* Empty State Preview */}
            <Card className="border-2 border-dashed border-muted-foreground/20 bg-muted/20">
                <CardContent className="text-center py-8">
                    <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
                        <Heart className="w-8 h-8 text-muted-foreground" />
                    </div>
                    <h4 className="font-semibold text-foreground mb-2">Start Building Your Collection</h4>
                    <p className="text-sm text-muted-foreground mb-4">
                        Save parts and models you're interested in for quick access later
                    </p>
                    <Button variant="outline" disabled>
                        <Search className="w-4 h-4 mr-2" />
                        Browse Parts
                    </Button>
                </CardContent>
            </Card>

            {/* Feature Highlights */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                {[
                    {
                        icon: Heart,
                        title: "Unlimited Storage",
                        description: "Save as many favorites as you want"
                    },
                    {
                        icon: Search,
                        title: "Quick Access",
                        description: "Find your saved items instantly"
                    },
                    {
                        icon: Star,
                        title: "Smart Organization",
                        description: "Auto-categorize by type and brand"
                    }
                ].map((feature, index) => (
                    <Card key={index} className="border-muted">
                        <CardContent className="p-4 text-center">
                            <feature.icon className="w-8 h-8 text-primary mx-auto mb-2" />
                            <h4 className="font-medium text-foreground text-sm mb-1">{feature.title}</h4>
                            <p className="text-xs text-muted-foreground">{feature.description}</p>
                        </CardContent>
                    </Card>
                ))}
            </div>
        </div>
    );
}
