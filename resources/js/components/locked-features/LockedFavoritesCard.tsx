import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Link } from '@inertiajs/react';
import { Crown, Heart, Lock, Star, Zap } from 'lucide-react';

interface LockedFavoritesCardProps {
    className?: string;
}

export function LockedFavoritesCard({ className }: LockedFavoritesCardProps) {
    return (
        <Card className={`relative overflow-hidden border-2 border-dashed border-primary/30 bg-gradient-to-br from-primary/5 to-secondary/5 ${className}`}>
            {/* Premium Badge */}
            <div className="absolute top-4 right-4">
                <Badge variant="secondary" className="bg-gradient-to-r from-amber-500 to-orange-500 text-white border-0">
                    <Crown className="w-3 h-3 mr-1" />
                    Premium
                </Badge>
            </div>

            <CardHeader className="text-center pb-4">
                <div className="mx-auto mb-4 relative">
                    <div className="w-16 h-16 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-full flex items-center justify-center">
                        <Heart className="w-8 h-8 text-primary" />
                    </div>
                    <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-muted rounded-full flex items-center justify-center border-2 border-background">
                        <Lock className="w-3 h-3 text-muted-foreground" />
                    </div>
                </div>
                <CardTitle className="text-xl font-bold text-foreground">
                    Favorites Collection
                </CardTitle>
                <CardDescription className="text-muted-foreground">
                    Save and organize your favorite parts and mobile models for quick access
                </CardDescription>
            </CardHeader>

            <CardContent className="space-y-6">
                {/* Feature Preview */}
                <div className="space-y-3">
                    <h4 className="font-semibold text-sm text-foreground flex items-center gap-2">
                        <Star className="w-4 h-4 text-primary" />
                        What you'll get with Premium:
                    </h4>
                    <ul className="space-y-2 text-sm text-muted-foreground">
                        <li className="flex items-center gap-2">
                            <div className="w-1.5 h-1.5 bg-primary rounded-full" />
                            Unlimited favorites storage
                        </li>
                        <li className="flex items-center gap-2">
                            <div className="w-1.5 h-1.5 bg-primary rounded-full" />
                            Quick access to saved parts
                        </li>
                        <li className="flex items-center gap-2">
                            <div className="w-1.5 h-1.5 bg-primary rounded-full" />
                            Organize by categories
                        </li>
                        <li className="flex items-center gap-2">
                            <div className="w-1.5 h-1.5 bg-primary rounded-full" />
                            Export favorites list
                        </li>
                        <li className="flex items-center gap-2">
                            <div className="w-1.5 h-1.5 bg-primary rounded-full" />
                            Sync across devices
                        </li>
                    </ul>
                </div>

                {/* Mock Favorites Preview */}
                <div className="space-y-3">
                    <h4 className="font-semibold text-sm text-foreground">Preview:</h4>
                    <div className="space-y-2">
                        {[
                            { name: "iPhone 14 Pro Display", category: "Display" },
                            { name: "Samsung Galaxy S23 Battery", category: "Battery" },
                            { name: "OnePlus 11 Camera Module", category: "Camera" }
                        ].map((item, index) => (
                            <div key={index} className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg border border-dashed border-muted-foreground/20">
                                <Heart className="w-4 h-4 text-muted-foreground" />
                                <div className="flex-1 min-w-0">
                                    <p className="text-sm font-medium text-muted-foreground truncate">{item.name}</p>
                                    <p className="text-xs text-muted-foreground/70">{item.category}</p>
                                </div>
                                <div className="w-6 h-6 bg-muted rounded flex items-center justify-center">
                                    <Lock className="w-3 h-3 text-muted-foreground" />
                                </div>
                            </div>
                        ))}
                    </div>
                </div>

                {/* Upgrade CTA */}
                <div className="pt-4 border-t border-border">
                    <Link href={route('subscription.plans')}>
                        <Button className="w-full bg-gradient-to-r from-primary to-secondary hover:from-primary/90 hover:to-secondary/90 text-white font-semibold">
                            <Zap className="w-4 h-4 mr-2" />
                            Upgrade to Premium
                        </Button>
                    </Link>
                    <p className="text-xs text-center text-muted-foreground mt-2">
                        Starting at $19/month • Cancel anytime
                    </p>
                </div>
            </CardContent>
        </Card>
    );
}
