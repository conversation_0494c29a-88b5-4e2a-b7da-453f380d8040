import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Link } from '@inertiajs/react';
import { ArrowRight, Check, Crown, Sparkles, Zap } from 'lucide-react';

interface UpgradePromptProps {
    title?: string;
    description?: string;
    features?: string[];
    className?: string;
    variant?: 'default' | 'compact' | 'banner';
}

export function UpgradePrompt({ 
    title = "Unlock Premium Features",
    description = "Get unlimited access to all features and remove ads",
    features = [
        "Unlimited searches",
        "Access to favorites",
        "Advanced filters",
        "Priority support",
        "Ad-free experience"
    ],
    className = "",
    variant = "default"
}: UpgradePromptProps) {
    
    if (variant === 'banner') {
        return (
            <div className={`bg-gradient-to-r from-primary/10 via-secondary/10 to-primary/10 border border-primary/20 rounded-lg p-4 ${className}`}>
                <div className="flex items-center justify-between gap-4">
                    <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center">
                            <Crown className="w-5 h-5 text-white" />
                        </div>
                        <div>
                            <h3 className="font-semibold text-foreground">{title}</h3>
                            <p className="text-sm text-muted-foreground">{description}</p>
                        </div>
                    </div>
                    <Link href={route('subscription.plans')}>
                        <Button className="bg-gradient-to-r from-primary to-secondary hover:from-primary/90 hover:to-secondary/90 text-white">
                            <Sparkles className="w-4 h-4 mr-2" />
                            Upgrade Now
                        </Button>
                    </Link>
                </div>
            </div>
        );
    }

    if (variant === 'compact') {
        return (
            <Card className={`border-primary/30 bg-gradient-to-br from-primary/5 to-secondary/5 ${className}`}>
                <CardContent className="p-4">
                    <div className="flex items-center gap-3 mb-3">
                        <Crown className="w-5 h-5 text-primary" />
                        <h3 className="font-semibold text-foreground">{title}</h3>
                        <Badge variant="secondary" className="ml-auto">Premium</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mb-4">{description}</p>
                    <Link href={route('subscription.plans')}>
                        <Button size="sm" className="w-full bg-gradient-to-r from-primary to-secondary hover:from-primary/90 hover:to-secondary/90 text-white">
                            <Zap className="w-4 h-4 mr-2" />
                            Upgrade
                        </Button>
                    </Link>
                </CardContent>
            </Card>
        );
    }

    return (
        <Card className={`border-primary/30 bg-gradient-to-br from-primary/5 to-secondary/5 ${className}`}>
            <CardHeader className="text-center">
                <div className="mx-auto mb-4">
                    <div className="w-16 h-16 bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center">
                        <Crown className="w-8 h-8 text-white" />
                    </div>
                </div>
                <CardTitle className="text-xl font-bold text-foreground flex items-center justify-center gap-2">
                    {title}
                    <Badge variant="secondary" className="bg-gradient-to-r from-amber-500 to-orange-500 text-white border-0">
                        Premium
                    </Badge>
                </CardTitle>
                <CardDescription className="text-muted-foreground">
                    {description}
                </CardDescription>
            </CardHeader>

            <CardContent className="space-y-6">
                {/* Features List */}
                <div className="space-y-3">
                    <h4 className="font-semibold text-sm text-foreground">Premium Benefits:</h4>
                    <ul className="space-y-2">
                        {features.map((feature, index) => (
                            <li key={index} className="flex items-center gap-3 text-sm text-muted-foreground">
                                <div className="w-5 h-5 bg-primary/20 rounded-full flex items-center justify-center">
                                    <Check className="w-3 h-3 text-primary" />
                                </div>
                                {feature}
                            </li>
                        ))}
                    </ul>
                </div>

                {/* Pricing Info */}
                <div className="bg-muted/50 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-foreground">$19<span className="text-sm font-normal text-muted-foreground">/month</span></div>
                    <p className="text-xs text-muted-foreground mt-1">Cancel anytime • 7-day free trial</p>
                </div>

                {/* CTA Buttons */}
                <div className="space-y-3">
                    <Link href={route('subscription.plans')}>
                        <Button className="w-full bg-gradient-to-r from-primary to-secondary hover:from-primary/90 hover:to-secondary/90 text-white font-semibold">
                            <Sparkles className="w-4 h-4 mr-2" />
                            Start Free Trial
                            <ArrowRight className="w-4 h-4 ml-2" />
                        </Button>
                    </Link>
                    <Link href={route('subscription.plans')}>
                        <Button variant="outline" className="w-full">
                            View All Plans
                        </Button>
                    </Link>
                </div>

                <p className="text-xs text-center text-muted-foreground">
                    Join thousands of professionals who trust our platform
                </p>
            </CardContent>
        </Card>
    );
}
