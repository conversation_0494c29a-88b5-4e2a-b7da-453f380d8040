import ColumnSelectionTable, { type ColumnInfo } from '@/components/ColumnSelectionTable';
import DynamicCompatibilityTable from '@/components/DynamicCompatibilityTable';
import PreviewDataTable from '@/components/PreviewDataTable';
import CompatibleModelsProtection from '@/components/security/CompatibleModelsProtection';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from '@/components/ui/dialog';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Label as UILabel } from '@/components/ui/label';
import AppLayout from '@/layouts/app-layout';
import type { CompatibilityColumns, Part } from '@/types';
import { Head, Link, router } from '@inertiajs/react';
import {
    ArrowLeft,
    Building,
    Calendar,
    ChevronLeft,
    ChevronRight,
    Download,
    Edit,
    ExternalLink,
    FileSpreadsheet,
    FileText,
    Hash,
    Image as ImageIcon,
    List,
    MoreHorizontal,
    Package,
    Settings,
    Smartphone,
    Table,
    Upload,
    X
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

interface Props {
    part: Part;
    showVerificationStatus?: boolean;
    compatibilityColumns?: CompatibilityColumns;
    isAdminView?: boolean;
    exportImportEnabled?: boolean;
}

export default function Show({ part, showVerificationStatus = true, compatibilityColumns, isAdminView = true, exportImportEnabled = false }: Props) {
    const [isImportDialogOpen, setIsImportDialogOpen] = useState(false);
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const [isImporting, setIsImporting] = useState(false);
    const [viewMode, setViewMode] = useState<'list' | 'table'>('table'); // Default to table view
    const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(null);
    const [isImagePreviewOpen, setIsImagePreviewOpen] = useState(false);

    // New state for column selection
    const [importStep, setImportStep] = useState<'file' | 'columns' | 'processing'>('file');
    const [columnPreview, setColumnPreview] = useState<ColumnInfo[]>([]);
    const [previewData, setPreviewData] = useState<Record<string, string>[]>([]);
    const [totalRows, setTotalRows] = useState<number>(0);
    const [isLoadingPreview, setIsLoadingPreview] = useState(false);

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const handleExport = () => {
        if (!exportImportEnabled) {
            toast.error('This feature is disabled for security reasons.');
            return;
        }
        window.location.href = `/admin/parts/${part.id}/export`;
        toast.success('Export started. Your download will begin shortly.');
    };

    const handleDownloadTemplate = () => {
        if (!exportImportEnabled) {
            toast.error('This feature is disabled for security reasons.');
            return;
        }
        window.location.href = `/admin/parts/${part.id}/compatibility/template`;
        toast.success('Template download started.');
    };

    const handleImportDialogOpen = () => {
        if (!exportImportEnabled) {
            toast.error('This feature is disabled for security reasons.');
            return;
        }
        setIsImportDialogOpen(true);
    };

    const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            setSelectedFile(file);
            handlePreviewFile(file);
        }
    };

    const handlePreviewFile = async (file: File) => {
        setIsLoadingPreview(true);
        setImportStep('columns');

        const formData = new FormData();
        formData.append('file', file);

        // Add CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        if (csrfToken) {
            formData.append('_token', csrfToken);
        }

        try {
            const response = await fetch(`/admin/parts/${part.id}/compatibility/preview`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                },
            });

            const data = await response.json();

            if (data.success) {
                setColumnPreview(data.columns);
                setPreviewData(data.previewRows);
                setTotalRows(data.totalRows);
                toast.success('File preview loaded successfully!');
            } else {
                toast.error(data.message || 'Failed to preview file');
                setImportStep('file');
            }
        } catch (error) {
            console.error('Preview error:', error);
            toast.error('Failed to preview file. Please try again.');
            setImportStep('file');
        } finally {
            setIsLoadingPreview(false);
        }
    };

    const getSelectedColumns = () => {
        // Return only selected columns
        return columnPreview.filter(col => col.isSelected).map(col => col.name);
    };

    const canProceedWithImport = () => {
        // Check if required columns are selected
        const requiredColumns = ['Brand', 'Model', 'Compatible'];
        const selectedColumnMappings = columnPreview.filter(col => col.isSelected).map(col => col.mappedTo);
        return requiredColumns.every(required => selectedColumnMappings.includes(required));
    };

    const resetImportDialog = () => {
        setIsImportDialogOpen(false);
        setSelectedFile(null);
        setIsImporting(false);
        setImportStep('file');
        setColumnPreview([]);
        setPreviewData([]);
        setTotalRows(0);
        setIsLoadingPreview(false);
    };

    const handleDialogClose = () => {
        if (!isImporting) {
            resetImportDialog();
        }
    };

    const handleBackToFileSelection = () => {
        setImportStep('file');
        setSelectedFile(null);
        setColumnPreview([]);
        setPreviewData([]);
        setTotalRows(0);
    };

    const handleColumnToggle = (columnName: string, isSelected: boolean) => {
        setColumnPreview(prev => prev.map(col =>
            col.name === columnName ? { ...col, isSelected } : col
        ));
    };

    const handleSelectAll = () => {
        setColumnPreview(prev => prev.map(col => ({ ...col, isSelected: true })));
    };

    const handleDeselectAll = () => {
        setColumnPreview(prev => prev.map(col => ({
            ...col,
            isSelected: col.isRequired // Keep required columns selected
        })));
    };

    const handleImport = () => {
        if (!selectedFile) {
            toast.error('Please select a CSV file to import.');
            return;
        }

        if (!canProceedWithImport()) {
            toast.error('Please select all required columns (Brand, Model, Compatible) to proceed with the import.');
            return;
        }

        console.log('Starting import...', {
            partId: part.id,
            fileName: selectedFile.name,
            fileSize: selectedFile.size,
            fileType: selectedFile.type,
            selectedColumns: getSelectedColumns()
        });

        setIsImporting(true);
        setImportStep('processing');

        const formData = new FormData();
        formData.append('file', selectedFile);

        // Add selected columns
        const selectedColumns = getSelectedColumns();
        selectedColumns.forEach(column => {
            formData.append('selected_columns[]', column);
        });

        // Add CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        if (csrfToken) {
            formData.append('_token', csrfToken);
        }

        console.log('FormData created, making request to:', `/admin/parts/${part.id}/compatibility/import`);

        router.post(`/admin/parts/${part.id}/compatibility/import`, formData, {
            onSuccess: (page) => {
                console.log('Import successful!', page);
                // Flash message will be handled by FlashMessageHandler component
                resetImportDialog();
                // Reload the page to show updated data
                router.reload({ only: ['part'] });
            },
            onError: (errors) => {
                console.error('Import errors:', errors);
                let errorMessage = 'Import failed. Please check your CSV file and try again.';

                if (errors.file) {
                    errorMessage = Array.isArray(errors.file) ? errors.file[0] : errors.file;

                    // Check if it's a format error
                    if (errorMessage.includes('model field is required') || errorMessage.includes('compatible field is required')) {
                        errorMessage = 'Invalid CSV format. Please download and use the compatibility import template. Do not use the parts export CSV format.';
                    }
                } else if (errors.message) {
                    errorMessage = errors.message;
                } else if (typeof errors === 'string') {
                    errorMessage = errors;
                }

                toast.error(errorMessage, {
                    duration: 6000, // Show longer for format errors
                });
                setIsImporting(false);
                setImportStep('columns'); // Go back to column selection
            },
            onFinish: () => {
                setIsImporting(false);
            }
        });
    };

    const handleImageClick = (index: number) => {
        setSelectedImageIndex(index);
        setIsImagePreviewOpen(true);
    };

    const handleCloseImagePreview = () => {
        setIsImagePreviewOpen(false);
        setSelectedImageIndex(null);
    };

    const handlePreviousImage = () => {
        if (selectedImageIndex !== null && part.images && selectedImageIndex > 0) {
            setSelectedImageIndex(selectedImageIndex - 1);
        }
    };

    const handleNextImage = () => {
        if (selectedImageIndex !== null && part.images && selectedImageIndex < part.images.length - 1) {
            setSelectedImageIndex(selectedImageIndex + 1);
        }
    };

    // Keyboard navigation for image gallery
    useEffect(() => {
        const handleKeyDown = (event: KeyboardEvent) => {
            if (!isImagePreviewOpen || selectedImageIndex === null || !part.images) return;

            switch (event.key) {
                case 'ArrowLeft':
                    event.preventDefault();
                    handlePreviousImage();
                    break;
                case 'ArrowRight':
                    event.preventDefault();
                    handleNextImage();
                    break;
                case 'Escape':
                    event.preventDefault();
                    handleCloseImagePreview();
                    break;
            }
        };

        if (isImagePreviewOpen) {
            document.addEventListener('keydown', handleKeyDown);
            // Prevent body scroll when modal is open
            document.body.style.overflow = 'hidden';
        }

        return () => {
            document.removeEventListener('keydown', handleKeyDown);
            document.body.style.overflow = 'unset';
        };
    }, [isImagePreviewOpen, selectedImageIndex, part.images]);

    // Table View Component for Compatible Models
    const CompatibleModelsTableView = () => (
        <CompatibleModelsProtection>
            <div className="border border-blue-200/50 dark:border-blue-800/50 rounded-lg overflow-hidden bg-white/50 dark:bg-gray-900/50">
                <div className="overflow-x-auto">
                    <table className="w-full">
                        <thead className="bg-gradient-to-r from-blue-100/80 to-indigo-100/80 dark:from-blue-900/40 dark:to-indigo-900/40">
                            <tr>
                                <th className="text-left p-3 font-semibold text-blue-900 dark:text-blue-100">Brand</th>
                                <th className="text-left p-3 font-semibold text-blue-900 dark:text-blue-100">Model</th>
                                <th className="text-left p-3 font-semibold text-blue-900 dark:text-blue-100 hidden sm:table-cell">Model Number</th>
                                <th className="text-left p-3 font-semibold text-blue-900 dark:text-blue-100 hidden lg:table-cell">Notes</th>
                                {showVerificationStatus && (
                                    <th className="text-left p-3 font-semibold text-blue-900 dark:text-blue-100">Status</th>
                                )}
                            </tr>
                        </thead>
                        <tbody>
                            {part.models?.map((model, index) => (
                                <tr key={model.id} className={`border-b border-blue-200/30 dark:border-blue-800/30 hover:bg-blue-50/50 dark:hover:bg-blue-950/20 transition-colors ${index % 2 === 0 ? 'bg-white/60 dark:bg-gray-800/30' : 'bg-blue-50/30 dark:bg-blue-950/10'
                                    }`}>
                                    <td className="p-3">
                                        <div className="flex items-center gap-2">
                                            {model.brand.logo_url && (
                                                <div className="w-6 h-6 rounded bg-white dark:bg-gray-800 p-1 border border-gray-200 dark:border-gray-700 flex items-center justify-center flex-shrink-0">
                                                    <img
                                                        src={model.brand.logo_url}
                                                        alt={model.brand.name}
                                                        className="w-4 h-4 object-contain"
                                                    />
                                                </div>
                                            )}
                                            <span className="font-semibold text-gray-900 dark:text-gray-100 text-sm">{model.brand.name}</span>
                                        </div>
                                    </td>
                                    <td className="p-3">
                                        <div>
                                            <p className="font-medium text-gray-800 dark:text-gray-200 text-sm">{model.name}</p>
                                            {model.model_number && (
                                                <p className="text-xs text-gray-500 dark:text-gray-400 font-mono sm:hidden">{model.model_number}</p>
                                            )}
                                            {model.pivot?.compatibility_notes && (
                                                <p className="text-xs text-gray-500 dark:text-gray-400 lg:hidden truncate mt-1">{model.pivot.compatibility_notes}</p>
                                            )}
                                        </div>
                                    </td>
                                    <td className="p-3 hidden sm:table-cell">
                                        <span className="text-sm text-gray-600 dark:text-gray-400 font-mono">
                                            {model.model_number || '-'}
                                        </span>
                                    </td>
                                    <td className="p-3 hidden lg:table-cell">
                                        <span className="text-sm text-gray-600 dark:text-gray-400">
                                            {model.pivot?.compatibility_notes || '-'}
                                        </span>
                                    </td>
                                    {showVerificationStatus && (
                                        <td className="p-3">
                                            <Badge
                                                variant={model.pivot?.is_verified ? "default" : "secondary"}
                                                className={`text-xs font-medium ${model.pivot?.is_verified
                                                    ? 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800'
                                                    : 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800'
                                                    }`}
                                            >
                                                {model.pivot?.is_verified ? 'Verified' : 'Unverified'}
                                            </Badge>
                                        </td>
                                    )}
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>
        </CompatibleModelsProtection>
    );

    // List View Component for Compatible Models
    const CompatibleModelsListView = () => (
        <CompatibleModelsProtection>
            <div className="space-y-2">
                {part.models?.map((model, index) => (
                    <div key={model.id} className={`flex items-center justify-between p-3 border border-blue-200/50 dark:border-blue-800/50 rounded-lg hover:bg-blue-50/50 dark:hover:bg-blue-950/20 transition-all duration-200 ${index % 2 === 0 ? 'bg-white/60 dark:bg-gray-800/30' : 'bg-blue-50/30 dark:bg-blue-950/10'
                        }`}>
                        <div className="flex items-center gap-3 flex-1 min-w-0">
                            {model.brand.logo_url && (
                                <div className="w-8 h-8 rounded bg-white dark:bg-gray-800 p-1 border border-gray-200 dark:border-gray-700 flex items-center justify-center flex-shrink-0">
                                    <img
                                        src={model.brand.logo_url}
                                        alt={model.brand.name}
                                        className="w-6 h-6 object-contain"
                                    />
                                </div>
                            )}
                            <div className="flex-1 min-w-0">
                                <div className="flex items-center gap-2 mb-1">
                                    <p className="font-semibold text-gray-900 dark:text-gray-100 truncate text-sm">{model.brand.name}</p>
                                    <span className="text-gray-400 dark:text-gray-500">•</span>
                                    <p className="font-medium text-gray-800 dark:text-gray-200 truncate text-sm">{model.name}</p>
                                </div>
                                <div className="flex items-center gap-4 text-xs text-gray-600 dark:text-gray-400">
                                    {model.model_number && (
                                        <span className="font-mono">{model.model_number}</span>
                                    )}
                                    {model.pivot?.compatibility_notes && (
                                        <span className="truncate">
                                            {model.pivot.compatibility_notes}
                                        </span>
                                    )}
                                </div>
                            </div>
                        </div>
                        {showVerificationStatus && (
                            <div className="flex items-center gap-2 flex-shrink-0 ml-3">
                                <Badge
                                    variant={model.pivot?.is_verified ? "default" : "secondary"}
                                    className={`text-xs font-medium ${model.pivot?.is_verified
                                        ? 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800'
                                        : 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800'
                                        }`}
                                >
                                    {model.pivot?.is_verified ? 'Verified' : 'Unverified'}
                                </Badge>
                            </div>
                        )}
                    </div>
                ))}
            </div>
        </CompatibleModelsProtection>
    );

    return (
        <AppLayout>
            <Head title={`${part.name} - Parts - Admin`} />

            <div className="flex h-full flex-1 flex-col gap-3 rounded-xl p-3 sm:p-4 overflow-x-auto">
                <div className="space-y-3 sm:space-y-4">
                    {/* Header */}
                    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 rounded-xl border border-blue-200/50 dark:border-blue-800/50 p-4 mb-4">
                        {/* Navigation */}
                        <div className="mb-3">
                            <Link href="/admin/parts">
                                <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-foreground">
                                    <ArrowLeft className="w-4 h-4 mr-2" />
                                    Back to Parts
                                </Button>
                            </Link>
                        </div>

                        {/* Main Header Content */}
                        <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4">
                            {/* Title Section */}
                            <div className="flex-1 min-w-0">
                                <div className="flex flex-col sm:flex-row sm:items-center gap-3 mb-2">
                                    <h1 className="text-xl sm:text-2xl font-bold tracking-tight text-gray-900 dark:text-gray-100 truncate">
                                        {part.name}
                                    </h1>
                                    <div className="flex items-center gap-2 flex-shrink-0">
                                        <Badge
                                            variant={part.is_active ? "default" : "secondary"}
                                            className={part.is_active ? "bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800" : ""}
                                        >
                                            {part.is_active ? 'Active' : 'Inactive'}
                                        </Badge>
                                        {part.part_number && (
                                            <Badge variant="outline" className="font-mono text-xs">
                                                #{part.part_number}
                                            </Badge>
                                        )}
                                    </div>
                                </div>

                                {/* Compact Info Row */}
                                <div className="flex flex-wrap items-center gap-x-6 gap-y-2 text-sm text-muted-foreground mb-3">
                                    <div className="flex items-center gap-1">
                                        <Package className="w-3 h-3" />
                                        <span className="font-medium">{part.category.name}</span>
                                    </div>
                                    {part.manufacturer && (
                                        <div className="flex items-center gap-1">
                                            <Building className="w-3 h-3" />
                                            <span className="font-medium">{part.manufacturer}</span>
                                        </div>
                                    )}
                                    <div className="flex items-center gap-1">
                                        <Smartphone className="w-3 h-3" />
                                        <span className="font-medium">{part.models?.length || 0} models</span>
                                    </div>
                                    <div className="flex items-center gap-1">
                                        <Calendar className="w-3 h-3" />
                                        <span className="font-medium">{part.created_at ? new Date(part.created_at).toLocaleDateString() : 'N/A'}</span>
                                    </div>
                                </div>
                            </div>

                            {/* Action Buttons */}
                            <div className="flex flex-col sm:flex-row gap-2 flex-shrink-0">
                                {/* Primary Actions */}
                                <div className="flex gap-2">
                                    <Link href={`/admin/parts/${part.id}/edit`}>
                                        <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white">
                                            <Edit className="w-4 h-4 mr-2" />
                                            Edit Part
                                        </Button>
                                    </Link>
                                    <Link href={`/admin/parts/${part.id}/compatibility`}>
                                        <Button variant="outline" size="sm">
                                            <Settings className="w-4 h-4 mr-2" />
                                            <span className="hidden sm:inline">Manage Compatibility</span>
                                            <span className="sm:hidden">Manage</span>
                                        </Button>
                                    </Link>
                                </div>

                                {/* Secondary Actions Dropdown */}
                                <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                        <Button variant="outline" size="sm">
                                            <MoreHorizontal className="w-4 h-4" />
                                            <span className="sr-only">More actions</span>
                                        </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end" className="w-56">
                                        {exportImportEnabled && (
                                            <>
                                                <DropdownMenuItem onClick={handleImportDialogOpen}>
                                                    <Upload className="w-4 h-4 mr-2" />
                                                    Import Compatibility CSV
                                                </DropdownMenuItem>
                                                <DropdownMenuItem onClick={handleExport}>
                                                    <Download className="w-4 h-4 mr-2" />
                                                    Export Compatibility CSV
                                                </DropdownMenuItem>
                                                <DropdownMenuSeparator />
                                            </>
                                        )}
                                        <DropdownMenuItem asChild>
                                            <Link href={route('parts.show', part.slug || part.id)}>
                                                <ExternalLink className="w-4 h-4 mr-2" />
                                                View Public Page
                                            </Link>
                                        </DropdownMenuItem>
                                    </DropdownMenuContent>
                                </DropdownMenu>
                            </div>
                        </div>
                    </div>

                    {/* Import Dialog */}
                    <Dialog open={isImportDialogOpen} onOpenChange={handleDialogClose}>
                        <DialogContent className={`${importStep === 'columns' ? 'sm:max-w-6xl' : 'sm:max-w-[425px]'} max-h-[90vh] overflow-y-auto`}>
                            <DialogHeader>
                                <DialogTitle>
                                    {importStep === 'file' && 'Import Compatibility Data'}
                                    {importStep === 'columns' && 'Select Columns to Import'}
                                    {importStep === 'processing' && 'Importing Data...'}
                                </DialogTitle>
                                <DialogDescription>
                                    {importStep === 'file' && (
                                        <>
                                            Upload a CSV file to update compatibility information for {part.name}.
                                            <br />
                                            <span className="text-sm font-medium">Important:</span> Use the compatibility CSV format, not the parts export format.
                                        </>
                                    )}
                                    {importStep === 'columns' && (
                                        <>
                                            Review your CSV data and select which columns to import. Required columns (Brand, Model, Compatible) cannot be deselected.
                                        </>
                                    )}
                                    {importStep === 'processing' && (
                                        'Please wait while your data is being imported...'
                                    )}
                                </DialogDescription>
                            </DialogHeader>

                            {/* Step 1: File Selection */}
                            {importStep === 'file' && (
                                <div className="grid gap-4 py-4">
                                    <div className="space-y-2">
                                        <UILabel htmlFor="csv-file">CSV File</UILabel>
                                        <Input
                                            id="csv-file"
                                            type="file"
                                            accept=".csv"
                                            onChange={handleFileSelect}
                                            disabled={isLoadingPreview}
                                        />
                                        {selectedFile && (
                                            <p className="text-sm text-muted-foreground">
                                                Selected: {selectedFile.name}
                                            </p>
                                        )}
                                    </div>
                                    <div className="space-y-2">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={handleDownloadTemplate}
                                            className="w-full"
                                        >
                                            <FileSpreadsheet className="w-4 h-4 mr-2" />
                                            Download Template
                                        </Button>
                                        <div className="text-xs text-muted-foreground space-y-1">
                                            <p>Download the CSV template with sample data and correct column format.</p>
                                            <p className="text-xs">The template includes all supported columns with example data to guide your import.</p>
                                        </div>
                                    </div>
                                </div>
                            )}

                            {/* Step 2: Data Preview */}
                            {importStep === 'columns' && (
                                <div className="py-4 space-y-6">
                                    {isLoadingPreview ? (
                                        <div className="flex items-center justify-center py-8">
                                            <div className="text-center">
                                                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-2"></div>
                                                <p className="text-sm text-gray-600">Analyzing CSV file...</p>
                                            </div>
                                        </div>
                                    ) : (
                                        <>
                                            {columnPreview.length > 0 && (
                                                <div className="space-y-6">
                                                    <ColumnSelectionTable
                                                        columns={columnPreview}
                                                        onColumnToggle={handleColumnToggle}
                                                        onSelectAll={handleSelectAll}
                                                        onDeselectAll={handleDeselectAll}
                                                    />

                                                    <PreviewDataTable
                                                        columns={columnPreview.map(col => col.name)}
                                                        data={previewData}
                                                        totalRows={totalRows}
                                                        maxRows={3}
                                                    />
                                                </div>
                                            )}
                                        </>
                                    )}
                                </div>
                            )}

                            {/* Step 3: Processing */}
                            {importStep === 'processing' && (
                                <div className="py-8">
                                    <div className="flex items-center justify-center">
                                        <div className="text-center">
                                            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                                            <p className="text-lg font-medium">Importing compatibility data...</p>
                                            <p className="text-sm text-gray-600 mt-2">This may take a few moments</p>
                                        </div>
                                    </div>
                                </div>
                            )}

                            <DialogFooter className="sm:justify-start">
                                {importStep === 'file' && (
                                    <Button
                                        variant="outline"
                                        onClick={handleDialogClose}
                                        disabled={isLoadingPreview}
                                    >
                                        Cancel
                                    </Button>
                                )}

                                {importStep === 'columns' && (
                                    <>
                                        <Button
                                            variant="outline"
                                            onClick={handleBackToFileSelection}
                                            disabled={isLoadingPreview}
                                        >
                                            <ChevronLeft className="w-4 h-4 mr-1" />
                                            Back
                                        </Button>
                                        <Button
                                            onClick={handleImport}
                                            disabled={!canProceedWithImport() || isLoadingPreview}
                                        >
                                            Import Data
                                            <ChevronRight className="w-4 h-4 ml-1" />
                                        </Button>
                                    </>
                                )}

                                {importStep === 'processing' && (
                                    <Button variant="outline" disabled>
                                        Processing...
                                    </Button>
                                )}
                            </DialogFooter>
                        </DialogContent>
                    </Dialog>

                    {/* Image Preview Modal */}
                    <Dialog open={isImagePreviewOpen} onOpenChange={handleCloseImagePreview}>
                        <DialogContent className="max-w-7xl w-[95vw] h-[95vh] p-0 overflow-hidden border-0 bg-transparent shadow-2xl">
                            <DialogTitle className="sr-only">
                                {part.name} - Image Gallery
                            </DialogTitle>
                            <div className="relative w-full h-full bg-gradient-to-br from-gray-900 via-black to-gray-900 rounded-xl overflow-hidden">
                                {/* Header with close button and image info */}
                                <div className="absolute top-0 left-0 right-0 z-20 bg-gradient-to-b from-black/80 via-black/40 to-transparent p-4">
                                    <div className="flex items-center justify-between">
                                        <div className="text-white">
                                            <h3 className="text-lg font-semibold">{part.name}</h3>
                                            {part.images && part.images.length > 1 && selectedImageIndex !== null && (
                                                <p className="text-sm text-gray-300">
                                                    Image {selectedImageIndex + 1} of {part.images.length}
                                                </p>
                                            )}
                                        </div>
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            className="text-white hover:bg-white/20 rounded-full w-10 h-10 p-0 transition-all duration-200 hover:scale-110"
                                            onClick={handleCloseImagePreview}
                                        >
                                            <X className="w-5 h-5" />
                                        </Button>
                                    </div>
                                </div>

                                {/* Navigation Buttons */}
                                {part.images && part.images.length > 1 && (
                                    <>
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            className="absolute left-4 top-1/2 -translate-y-1/2 z-20 text-white hover:bg-white/20 rounded-full w-12 h-12 p-0 disabled:opacity-30 transition-all duration-200 hover:scale-110 disabled:hover:scale-100"
                                            onClick={handlePreviousImage}
                                            disabled={selectedImageIndex === 0}
                                        >
                                            <ChevronLeft className="w-6 h-6" />
                                        </Button>
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            className="absolute right-4 top-1/2 -translate-y-1/2 z-20 text-white hover:bg-white/20 rounded-full w-12 h-12 p-0 disabled:opacity-30 transition-all duration-200 hover:scale-110 disabled:hover:scale-100"
                                            onClick={handleNextImage}
                                            disabled={selectedImageIndex === (part.images?.length || 0) - 1}
                                        >
                                            <ChevronRight className="w-6 h-6" />
                                        </Button>
                                    </>
                                )}

                                {/* Main Image Container */}
                                {selectedImageIndex !== null && part.images && (
                                    <div className="flex items-center justify-center w-full h-full p-8 pt-20 pb-16">
                                        <div className="relative max-w-full max-h-full">
                                            <img
                                                src={part.images[selectedImageIndex]}
                                                alt={`${part.name} - Image ${selectedImageIndex + 1}`}
                                                className="max-w-full max-h-full object-contain rounded-lg shadow-2xl transition-all duration-300"
                                                onError={(e) => {
                                                    e.currentTarget.src = '/placeholder-image.svg';
                                                }}
                                            />
                                            {/* Loading overlay could be added here if needed */}
                                        </div>
                                    </div>
                                )}

                                {/* Bottom Controls */}
                                {part.images && part.images.length > 1 && selectedImageIndex !== null && (
                                    <div className="absolute bottom-0 left-0 right-0 z-20 bg-gradient-to-t from-black/80 via-black/40 to-transparent p-4">
                                        <div className="flex items-center justify-center space-x-2">
                                            {/* Thumbnail Navigation */}
                                            <div className="flex items-center space-x-2 bg-black/50 rounded-full px-4 py-2">
                                                {part.images.map((_, index) => (
                                                    <button
                                                        key={index}
                                                        onClick={() => setSelectedImageIndex(index)}
                                                        className={`w-2 h-2 rounded-full transition-all duration-200 ${index === selectedImageIndex
                                                            ? 'bg-white scale-125'
                                                            : 'bg-white/50 hover:bg-white/75'
                                                            }`}
                                                    />
                                                ))}
                                            </div>
                                        </div>
                                    </div>
                                )}

                                {/* Keyboard Navigation Hint */}
                                {part.images && part.images.length > 1 && (
                                    <div className="absolute bottom-4 right-4 z-20 text-white/60 text-xs bg-black/30 rounded px-2 py-1">
                                        Use ← → keys to navigate
                                    </div>
                                )}
                            </div>
                        </DialogContent>
                    </Dialog>

                    <div className="grid grid-cols-1 lg:grid-cols-4 gap-3 lg:gap-4">
                        {/* Main Information */}
                        <div className="lg:col-span-3 space-y-3 lg:space-y-4">
                            {/* Basic Information and Specifications Side by Side */}
                            <div className="grid grid-cols-1 xl:grid-cols-2 gap-3 lg:gap-4">
                                {/* Basic Information */}
                                <Card className="border-blue-200/50 dark:border-blue-800/50">
                                    <CardHeader className="bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-blue-950/10 dark:to-indigo-950/10 pb-3">
                                        <CardTitle className="flex items-center gap-2 text-blue-900 dark:text-blue-100 text-lg">
                                            <Package className="w-4 h-4" />
                                            Basic Information
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent className="p-0">
                                        {/* Basic Info Table */}
                                        <div className="border border-blue-200/50 dark:border-blue-800/50 rounded-lg overflow-hidden bg-white/50 dark:bg-gray-900/50 mx-4 mb-4">
                                            <div className="overflow-x-auto">
                                                <table className="w-full">
                                                    <tbody>
                                                        <tr className="border-b border-blue-200/30 dark:border-blue-800/30 hover:bg-blue-50/50 dark:hover:bg-blue-950/20 transition-colors bg-white/60 dark:bg-gray-800/30">
                                                            <td className="p-3 w-1/3">
                                                                <span className="font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
                                                                    <Package className="w-3 h-3 text-blue-600 dark:text-blue-400" />
                                                                    Category
                                                                </span>
                                                            </td>
                                                            <td className="p-3">
                                                                <span className="text-gray-800 dark:text-gray-200 font-medium">
                                                                    {part.category.name}
                                                                </span>
                                                            </td>
                                                        </tr>
                                                        {part.part_number && (
                                                            <tr className="border-b border-blue-200/30 dark:border-blue-800/30 hover:bg-blue-50/50 dark:hover:bg-blue-950/20 transition-colors bg-blue-50/30 dark:bg-blue-950/10">
                                                                <td className="p-3">
                                                                    <span className="font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
                                                                        <Hash className="w-3 h-3 text-green-600 dark:text-green-400" />
                                                                        Part Number
                                                                    </span>
                                                                </td>
                                                                <td className="p-3">
                                                                    <span className="text-gray-800 dark:text-gray-200 font-medium font-mono">
                                                                        {part.part_number}
                                                                    </span>
                                                                </td>
                                                            </tr>
                                                        )}
                                                        {part.manufacturer && (
                                                            <tr className="border-b border-blue-200/30 dark:border-blue-800/30 hover:bg-blue-50/50 dark:hover:bg-blue-950/20 transition-colors bg-white/60 dark:bg-gray-800/30">
                                                                <td className="p-3">
                                                                    <span className="font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
                                                                        <Building className="w-3 h-3 text-purple-600 dark:text-purple-400" />
                                                                        Manufacturer
                                                                    </span>
                                                                </td>
                                                                <td className="p-3">
                                                                    <span className="text-gray-800 dark:text-gray-200 font-medium">
                                                                        {part.manufacturer}
                                                                    </span>
                                                                </td>
                                                            </tr>
                                                        )}
                                                        <tr className="hover:bg-blue-50/50 dark:hover:bg-blue-950/20 transition-colors bg-blue-50/30 dark:bg-blue-950/10">
                                                            <td className="p-3">
                                                                <span className="font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
                                                                    <Calendar className="w-3 h-3 text-orange-600 dark:text-orange-400" />
                                                                    Created
                                                                </span>
                                                            </td>
                                                            <td className="p-3">
                                                                <span className="text-gray-800 dark:text-gray-200 font-medium">
                                                                    {part.created_at ? formatDate(part.created_at) : 'N/A'}
                                                                </span>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>

                                        {/* Description */}
                                        {part.description && (
                                            <div className="mx-4 mb-4 p-3 bg-gradient-to-br from-gray-50 to-slate-50 dark:from-gray-900/50 dark:to-slate-900/50 rounded-lg border border-gray-200 dark:border-gray-700">
                                                <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-1 mb-2">
                                                    <FileText className="w-3 h-3" />
                                                    Description
                                                </Label>
                                                <p className="text-sm leading-relaxed whitespace-pre-wrap text-gray-800 dark:text-gray-200">{part.description}</p>
                                            </div>
                                        )}
                                    </CardContent>
                                </Card>

                                {/* Specifications */}
                                {part.specifications && Object.keys(part.specifications).length > 0 && (
                                    <Card className="border-blue-200/50 dark:border-blue-800/50">
                                        <CardHeader className="bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-blue-950/10 dark:to-indigo-950/10 pb-3">
                                            <CardTitle className="flex items-center gap-2 text-blue-900 dark:text-blue-100 text-lg">
                                                <Settings className="w-4 h-4" />
                                                Specifications
                                            </CardTitle>
                                        </CardHeader>
                                        <CardContent className="p-0">
                                            <div className="border border-blue-200/50 dark:border-blue-800/50 rounded-lg overflow-hidden bg-white/50 dark:bg-gray-900/50 mx-4 mb-4">
                                                <div className="overflow-x-auto">
                                                    <table className="w-full">
                                                        <thead className="bg-gradient-to-r from-blue-100/80 to-indigo-100/80 dark:from-blue-900/40 dark:to-indigo-900/40">
                                                            <tr>
                                                                <th className="text-left p-3 font-semibold text-blue-900 dark:text-blue-100 w-1/2">Specification</th>
                                                                <th className="text-left p-3 font-semibold text-blue-900 dark:text-blue-100">Value</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            {Object.entries(part.specifications).map(([key, value], index) => (
                                                                <tr key={key} className={`border-b border-blue-200/30 dark:border-blue-800/30 hover:bg-blue-50/50 dark:hover:bg-blue-950/20 transition-colors ${index % 2 === 0 ? 'bg-white/60 dark:bg-gray-800/30' : 'bg-blue-50/30 dark:bg-blue-950/10'
                                                                    }`}>
                                                                    <td className="p-3">
                                                                        <span className="font-semibold text-gray-900 dark:text-gray-100 capitalize">
                                                                            {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                                                                        </span>
                                                                    </td>
                                                                    <td className="p-3">
                                                                        <span className="text-gray-800 dark:text-gray-200 font-medium">
                                                                            {value}
                                                                        </span>
                                                                    </td>
                                                                </tr>
                                                            ))}
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>
                                )}
                            </div>

                            {/* Compatible Models */}
                            {part.models && part.models.length > 0 && (
                                <Card className="border-blue-200/50 dark:border-blue-800/50">
                                    <CardHeader className="bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-blue-950/10 dark:to-indigo-950/10 pb-3">
                                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                                            <div className="flex-1 min-w-0">
                                                <CardTitle className="flex items-center gap-2 text-blue-900 dark:text-blue-100 text-lg">
                                                    <Smartphone className="w-4 h-4" />
                                                    Compatible Models ({part.models.length})
                                                </CardTitle>
                                                <CardDescription className="mt-1 text-blue-700/70 dark:text-blue-300/70 text-sm">
                                                    Mobile device models that are compatible with this part
                                                </CardDescription>
                                            </div>
                                            {/* View Mode Toggle */}
                                            <div className="flex items-center gap-1 bg-white/60 dark:bg-gray-800/60 border border-blue-200 dark:border-blue-700 rounded-lg p-1">
                                                <Button
                                                    variant={viewMode === 'table' ? 'default' : 'ghost'}
                                                    size="sm"
                                                    onClick={() => setViewMode('table')}
                                                    className={`h-7 px-2 ${viewMode === 'table'
                                                        ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-sm'
                                                        : 'hover:bg-blue-100 dark:hover:bg-blue-900/50 text-blue-700 dark:text-blue-300'
                                                        }`}
                                                    title="Table View"
                                                >
                                                    <Table className="h-3 w-3" />
                                                    <span className="ml-1 hidden sm:inline text-xs">Table</span>
                                                </Button>
                                                <Button
                                                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                                                    size="sm"
                                                    onClick={() => setViewMode('list')}
                                                    className={`h-7 px-2 ${viewMode === 'list'
                                                        ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-sm'
                                                        : 'hover:bg-blue-100 dark:hover:bg-blue-900/50 text-blue-700 dark:text-blue-300'
                                                        }`}
                                                    title="List View"
                                                >
                                                    <List className="h-3 w-3" />
                                                    <span className="ml-1 hidden sm:inline text-xs">List</span>
                                                </Button>
                                            </div>
                                        </div>
                                    </CardHeader>
                                    <CardContent className="p-4">
                                        {/* Conditional rendering based on view mode */}
                                        {viewMode === 'table' ? (
                                            compatibilityColumns ? (
                                                <CompatibleModelsProtection>
                                                    <DynamicCompatibilityTable
                                                        part={part}
                                                        compatibilityColumns={compatibilityColumns}
                                                        isAdminView={isAdminView}
                                                        enableColumnResize={isAdminView}
                                                    />
                                                </CompatibleModelsProtection>
                                            ) : (
                                                <CompatibleModelsTableView />
                                            )
                                        ) : (
                                            <CompatibleModelsListView />
                                        )}
                                    </CardContent>
                                </Card>
                            )}
                        </div>

                        {/* Sidebar */}
                        <div className="space-y-4">
                            {/* Combined Images and Actions */}
                            <Card className="border-blue-200/50 dark:border-blue-800/50">
                                {/* Images Section */}
                                {part.images && part.images.length > 0 && (
                                    <>
                                        <CardHeader className="bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-blue-950/10 dark:to-indigo-950/10 pb-3">
                                            <CardTitle className="flex items-center gap-2 text-blue-900 dark:text-blue-100 text-lg">
                                                <ImageIcon className="w-4 h-4" />
                                                Images ({part.images.length})
                                            </CardTitle>
                                        </CardHeader>
                                        <CardContent className="p-3 pb-0">
                                            <div className="grid grid-cols-3 gap-2">
                                                {part.images.map((image, index) => (
                                                    <div
                                                        key={index}
                                                        className="aspect-square w-full max-w-[80px] mx-auto border border-blue-200/50 dark:border-blue-800/50 rounded-lg overflow-hidden group cursor-pointer hover:shadow-lg hover:shadow-blue-500/20 transition-all duration-300"
                                                        onClick={() => handleImageClick(index)}
                                                    >
                                                        <img
                                                            src={image}
                                                            alt={`${part.name} - Image ${index + 1}`}
                                                            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                                                            onError={(e) => {
                                                                e.currentTarget.src = '/placeholder-image.svg';
                                                            }}
                                                        />
                                                    </div>
                                                ))}
                                            </div>
                                        </CardContent>
                                    </>
                                )}

                                {/* Quick Actions Section */}
                                <CardHeader className={`bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-blue-950/10 dark:to-indigo-950/10 pb-3 ${part.images && part.images.length > 0 ? 'border-t border-blue-200/50 dark:border-blue-800/50 mt-3' : ''}`}>
                                    <CardTitle className="flex items-center gap-2 text-blue-900 dark:text-blue-100 text-lg">
                                        <Settings className="w-4 h-4" />
                                        Quick Actions
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-2 p-3">
                                    {exportImportEnabled && (
                                        <>
                                            <div className="space-y-2">
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    className="w-full justify-start hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-950/20 transition-colors text-sm"
                                                    onClick={handleImportDialogOpen}
                                                >
                                                    <Upload className="w-3 h-3 mr-2" />
                                                    Import Compatibility CSV
                                                </Button>
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    className="w-full justify-start hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-950/20 transition-colors text-sm"
                                                    onClick={handleExport}
                                                >
                                                    <Download className="w-3 h-3 mr-2" />
                                                    Export Compatibility CSV
                                                </Button>
                                            </div>
                                            <div className="border-t border-gray-200 dark:border-gray-700 my-2"></div>
                                        </>
                                    )}
                                    <div className="space-y-2">
                                        <Link href={route('parts.show', part.slug || part.id)} className="block">
                                            <Button variant="outline" size="sm" className="w-full justify-start hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-950/20 transition-colors text-sm">
                                                <ExternalLink className="w-3 h-3 mr-2" />
                                                View Public Page
                                            </Button>
                                        </Link>
                                        <Link href={`/admin/parts/${part.id}/edit`} className="block">
                                            <Button variant="outline" size="sm" className="w-full justify-start hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-950/20 transition-colors text-sm">
                                                <Edit className="w-3 h-3 mr-2" />
                                                Edit Part
                                            </Button>
                                        </Link>
                                        <Link href={`/admin/parts/${part.id}/compatibility`} className="block">
                                            <Button variant="outline" size="sm" className="w-full justify-start hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-950/20 transition-colors text-sm">
                                                <Settings className="w-3 h-3 mr-2" />
                                                Manage Compatibility
                                            </Button>
                                        </Link>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}

// Helper component for labels
function Label({ children, className = "" }: { children: React.ReactNode; className?: string }) {
    return <label className={`block text-sm font-medium ${className}`}>{children}</label>;
}
