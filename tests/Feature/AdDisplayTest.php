<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AdDisplayTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function free_user_sees_ads_on_locked_favorites_page()
    {
        $user = User::factory()->create([
            'subscription_plan' => 'free'
        ]);

        $response = $this->actingAs($user)->get(route('dashboard.favorites'));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('dashboard/favorites')
                ->where('isPremium', false)
        );
    }

    /** @test */
    public function premium_user_does_not_see_ads_on_favorites_page()
    {
        $user = User::factory()->create([
            'subscription_plan' => 'premium'
        ]);

        $response = $this->actingAs($user)->get(route('dashboard.favorites'));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('dashboard/favorites')
                ->where('isPremium', true)
        );
    }

    /** @test */
    public function admin_user_does_not_see_ads()
    {
        $admin = User::factory()->create([
            'is_admin' => true,
            'role' => 'admin'
        ]);

        $response = $this->actingAs($admin)->get(route('dashboard.favorites'));

        $response->assertStatus(200);
        // Admin should see full functionality without ads
        $response->assertInertia(fn ($page) => 
            $page->component('dashboard/favorites')
                ->where('isPremium', true) // Admin is treated as premium
        );
    }

    /** @test */
    public function guest_user_cannot_access_favorites_page()
    {
        $response = $this->get(route('dashboard.favorites'));

        // Should redirect to login
        $response->assertRedirect('/login');
    }
}
