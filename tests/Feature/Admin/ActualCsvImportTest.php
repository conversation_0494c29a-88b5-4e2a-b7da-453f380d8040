<?php

namespace Tests\Feature\Admin;

use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use App\Models\User;
use App\Services\CompatibilityColumnService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Tests\TestCase;

class ActualCsvImportTest extends TestCase
{
    use RefreshDatabase;

    protected $admin;
    protected $brand;
    protected $category;
    protected $model;
    protected $part;
    protected $columnService;

    protected function setUp(): void
    {
        parent::setUp();

        // Enable export/import functionality for tests
        \App\Models\SiteSetting::updateOrCreate(
            ['key' => 'admin_parts_export_import_enabled'],
            ['value' => '1', 'type' => 'boolean', 'is_active' => true]
        );

        // Clear all related caches
        \Illuminate\Support\Facades\Cache::flush();

        // Create admin user with correct email and admin privileges
        $this->admin = User::factory()->create([
            'email' => '<EMAIL>',
            'is_admin' => true,
            'role' => 'admin',
            'status' => 'active',
            'approval_status' => 'approved'
        ]);
        
        $this->brand = Brand::factory()->create(['name' => 'Vivo']);
        $this->category = Category::factory()->create(['name' => 'Lcd']);
        $this->model = MobileModel::factory()->create([
            'name' => 'Y20',
            'brand_id' => $this->brand->id,
            'model_number' => 'V2029, V2029_PK'
        ]);
        $this->part = Part::factory()->create([
            'name' => 'Lcd 6.5',
            'part_number' => 'lcd-642t47',
            'category_id' => $this->category->id
        ]);

        $this->columnService = app(CompatibilityColumnService::class);
    }

    /** @test */
    public function it_auto_enables_columns_from_actual_csv_file()
    {
        $this->actingAs($this->admin);

        // First disable all enhanced columns to test auto-enable functionality
        $initialConfig = $this->columnService->getColumnConfiguration(true);
        $enhancedFields = [
            'front_camera_type', 'camera_position', 'battery_mah', 
            'pin_model', 'connector_types', 'types', 'additional_info'
        ];
        
        // Disable all enhanced fields
        foreach ($enhancedFields as $field) {
            $initialConfig[$field]['enabled'] = false;
        }
        $this->columnService->updateColumnConfiguration($initialConfig);
        
        // Verify they are disabled
        $disabledConfig = $this->columnService->getColumnConfiguration(true);
        foreach ($enhancedFields as $field) {
            $this->assertFalse($disabledConfig[$field]['enabled'], "Field {$field} should be initially disabled");
        }

        // Create CSV with the exact content from the user's file
        $csvContent = "ID,Brand,Model,Model Number,Parts Name,Part Number,Description,Manufacturer,Category,Display Types,Display Size,Location,Front Camera Type,Camera Position,Battery mAh,Pin & Model,Connector Types,Types,Compatible,Verified,Notes,Additional Info\n";
        $csvContent .= "{$this->part->id},Vivo,Y20,\"V2029, V2029_PK\",Lcd 6.5,lcd-642t47,\"Neque porro quisquam est qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit\",Samsung,Lcd,Old,LCD,Center,Sony IMX 680,Center,6000mah,Type - C,USB,USB-3,true,true,\"Neque porro quisquam est qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit\",\"Neque porro quisquam est qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit\"\n";

        $file = UploadedFile::fake()->createWithContent('actual_test.csv', $csvContent);

        $response = $this->post("/admin/parts/{$this->part->id}/compatibility/import", [
            'file' => $file,
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify all enhanced columns are now enabled
        $updatedConfig = $this->columnService->getColumnConfiguration(true);
        foreach ($enhancedFields as $field) {
            $this->assertTrue($updatedConfig[$field]['enabled'], "Field {$field} should be auto-enabled after import");
        }

        // Verify the data was imported correctly with the exact values from the CSV
        $compatibility = $this->part->models()->where('model_id', $this->model->id)->first();
        $this->assertNotNull($compatibility);
        $this->assertEquals('Sony IMX 680', $compatibility->pivot->front_camera_type);
        $this->assertEquals('Center', $compatibility->pivot->camera_position);
        $this->assertEquals('6000mah', $compatibility->pivot->battery_mah);
        $this->assertEquals('Type - C', $compatibility->pivot->pin_model);
        $this->assertEquals('USB', $compatibility->pivot->connector_types);
        $this->assertEquals('USB-3', $compatibility->pivot->types);
        $this->assertEquals('Neque porro quisquam est qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit', $compatibility->pivot->additional_info);
    }

    /** @test */
    public function it_handles_csv_with_quoted_fields_correctly()
    {
        $this->actingAs($this->admin);

        // Create CSV with quoted fields that contain commas (like the actual file)
        $csvContent = "ID,Brand,Model,Model Number,Parts Name,Part Number,Description,Manufacturer,Category,Display Types,Display Size,Location,Front Camera Type,Camera Position,Battery mAh,Pin & Model,Connector Types,Types,Compatible,Verified,Notes,Additional Info\n";
        $csvContent .= "{$this->part->id},Vivo,Y20,\"V2029, V2029_PK\",\"Lcd 6.5\",\"lcd-642t47\",\"Description with, commas\",Samsung,Lcd,Old,LCD,Center,\"Sony IMX 680\",Center,\"6000mah\",\"Type - C\",USB,\"USB-3\",true,true,\"Notes with, commas\",\"Additional info with, commas\"\n";

        $file = UploadedFile::fake()->createWithContent('quoted_test.csv', $csvContent);

        $response = $this->post("/admin/parts/{$this->part->id}/compatibility/import", [
            'file' => $file,
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify the data was imported correctly, handling quoted fields
        $compatibility = $this->part->models()->where('model_id', $this->model->id)->first();
        $this->assertNotNull($compatibility);
        $this->assertEquals('Sony IMX 680', $compatibility->pivot->front_camera_type);
        $this->assertEquals('Type - C', $compatibility->pivot->pin_model);
        $this->assertEquals('Additional info with, commas', $compatibility->pivot->additional_info);
    }

    /** @test */
    public function it_verifies_fix_works_with_original_issue_scenario()
    {
        $this->actingAs($this->admin);

        // This test specifically verifies that the original issue is fixed:
        // Enhanced CSV fields should be auto-enabled after import

        // Start with all enhanced fields disabled (simulating the original issue)
        $config = $this->columnService->getColumnConfiguration(true);
        $enhancedFields = ['front_camera_type', 'camera_position', 'battery_mah', 'pin_model', 'connector_types', 'types', 'additional_info'];
        
        foreach ($enhancedFields as $field) {
            $config[$field]['enabled'] = false;
        }
        $this->columnService->updateColumnConfiguration($config);

        // Import CSV with enhanced fields
        $csvContent = "ID,Brand,Model,Front Camera Type,Camera Position,Battery mAh,Pin & Model,Connector Types,Types,Additional Info,Compatible,Verified\n";
        $csvContent .= "{$this->part->id},Vivo,Y20,12MP,Front,4000mAh,USB-C,USB-C,Premium,Enhanced CSV test,true,true\n";

        $file = UploadedFile::fake()->createWithContent('fix_verification.csv', $csvContent);

        $response = $this->post("/admin/parts/{$this->part->id}/compatibility/import", [
            'file' => $file,
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // The fix should have auto-enabled all columns that had data
        $updatedConfig = $this->columnService->getColumnConfiguration(true);
        $expectedEnabledFields = ['front_camera_type', 'camera_position', 'battery_mah', 'pin_model', 'connector_types', 'types', 'additional_info'];
        
        foreach ($expectedEnabledFields as $field) {
            $this->assertTrue($updatedConfig[$field]['enabled'], "Field {$field} should be auto-enabled after import (this was the original issue)");
        }
    }
}
