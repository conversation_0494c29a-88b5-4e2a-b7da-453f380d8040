<?php

namespace Tests\Feature\Admin;

use App\Models\SiteSetting;
use App\Models\User;
use App\Services\CompatibilityColumnService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class CompatibilityColumnManagerTest extends TestCase
{
    use RefreshDatabase;

    private User $admin;
    private User $user;
    private CompatibilityColumnService $columnService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->admin = User::factory()->create(['role' => 'admin']);
        $this->user = User::factory()->create(['role' => 'user']);
        $this->columnService = app(CompatibilityColumnService::class);
    }

    /** @test */
    public function site_settings_page_loads_with_compatibility_columns_manager()
    {
        $response = $this->actingAs($this->admin)
            ->get('/admin/site-settings');

        $response->assertOk();
        
        // Should include the parts_compatibility_columns setting
        $response->assertInertia(fn ($page) => 
            $page->has('settings')
                ->where('categories', fn ($categories) => 
                    in_array('parts_management', $categories)
                )
        );
    }

    /** @test */
    public function compatibility_columns_manager_receives_correct_initial_data()
    {
        // Set up a specific configuration
        $testConfig = [
            'model_name' => [
                'enabled' => true,
                'required' => true,
                'order' => 1,
                'label' => 'Test Model',
                'source' => 'model.name',
                'priority' => 1,
                'minBreakpoint' => 'xs'
            ],
            'brand_name' => [
                'enabled' => false,
                'required' => false,
                'order' => 2,
                'label' => 'Test Brand',
                'source' => 'model.brand.name',
                'priority' => 2,
                'minBreakpoint' => 'sm'
            ]
        ];

        SiteSetting::set('parts_compatibility_columns', $testConfig, 'json');

        $response = $this->actingAs($this->admin)
            ->get('/admin/site-settings');

        $response->assertOk();
        
        // Should include the test configuration
        $response->assertInertia(fn ($page) => 
            $page->has('settings.parts_management')
                ->where('settings.parts_management.0.value', $testConfig)
        );
    }

    /** @test */
    public function column_configuration_update_via_site_settings_works()
    {
        $newConfig = [
            'model_name' => [
                'enabled' => true,
                'required' => true,
                'order' => 1,
                'label' => 'Updated Model',
                'source' => 'model.name',
                'priority' => 1,
                'minBreakpoint' => 'xs'
            ],
            'brand_name' => [
                'enabled' => true,
                'required' => false,
                'order' => 2,
                'label' => 'Updated Brand',
                'source' => 'model.brand.name',
                'priority' => 2,
                'minBreakpoint' => 'md'
            ]
        ];

        $response = $this->actingAs($this->admin)
            ->postJson('/admin/parts/compatibility-columns/config', [
                'columns' => $newConfig
            ]);

        $response->assertOk()
            ->assertJson([
                'success' => true,
                'data' => $newConfig
            ]);

        // Verify the configuration was saved
        $savedConfig = SiteSetting::get('parts_compatibility_columns');
        $this->assertEquals($newConfig, $savedConfig);
    }

    /** @test */
    public function column_manager_handles_validation_errors_correctly()
    {
        $invalidConfig = [
            'model_name' => [
                'enabled' => 'not_boolean',
                'required' => 'not_boolean',
                'order' => 'not_integer',
                'label' => '', // Empty label should fail
                'source' => '', // Empty source should fail
                'priority' => 'not_integer',
                'minBreakpoint' => 'invalid_breakpoint'
            ]
        ];

        $response = $this->actingAs($this->admin)
            ->postJson('/admin/parts/compatibility-columns/config', [
                'columns' => $invalidConfig
            ]);

        $response->assertStatus(422);
        
        // Should return validation errors
        $response->assertJsonValidationErrors([
            'columns.model_name.enabled',
            'columns.model_name.required',
            'columns.model_name.order',
            'columns.model_name.label',
            'columns.model_name.source',
            'columns.model_name.priority',
            'columns.model_name.minBreakpoint'
        ]);
    }

    /** @test */
    public function column_manager_prevents_disabling_required_columns()
    {
        // Set up a configuration with a required column
        $config = $this->columnService->getColumnConfiguration(true);
        $config['model_name']['required'] = true;
        $config['model_name']['enabled'] = true;
        $this->columnService->updateColumnConfiguration($config);

        // Try to disable the required column
        $config['model_name']['enabled'] = false;

        $response = $this->actingAs($this->admin)
            ->postJson('/admin/parts/compatibility-columns/validate', [
                'columns' => $config
            ]);

        $response->assertOk()
            ->assertJson([
                'success' => false,
                'errors' => [
                    'At least one required column must be enabled'
                ]
            ]);
    }

    /** @test */
    public function column_manager_allows_valid_configurations()
    {
        $validConfig = [
            'model_name' => [
                'enabled' => true,
                'required' => true,
                'order' => 1,
                'label' => 'Device Model',
                'source' => 'model.name',
                'priority' => 1,
                'minBreakpoint' => 'xs'
            ],
            'brand_name' => [
                'enabled' => true,
                'required' => false,
                'order' => 2,
                'label' => 'Brand Name',
                'source' => 'model.brand.name',
                'priority' => 2,
                'minBreakpoint' => 'sm'
            ]
        ];

        $response = $this->actingAs($this->admin)
            ->postJson('/admin/parts/compatibility-columns/validate', [
                'columns' => $validConfig
            ]);

        $response->assertOk()
            ->assertJson([
                'success' => true,
                'errors' => []
            ]);
    }

    /** @test */
    public function column_manager_handles_column_ordering()
    {
        $config = [
            'model_name' => [
                'enabled' => true,
                'required' => true,
                'order' => 5,
                'label' => 'Model',
                'source' => 'model.name',
                'priority' => 1,
                'minBreakpoint' => 'xs'
            ],
            'brand_name' => [
                'enabled' => true,
                'required' => false,
                'order' => 1,
                'label' => 'Brand',
                'source' => 'model.brand.name',
                'priority' => 2,
                'minBreakpoint' => 'xs'
            ],
            'category_name' => [
                'enabled' => true,
                'required' => false,
                'order' => 3,
                'label' => 'Category',
                'source' => 'part.category.name',
                'priority' => 3,
                'minBreakpoint' => 'xs'
            ]
        ];

        $this->actingAs($this->admin)
            ->postJson('/admin/parts/compatibility-columns/config', [
                'columns' => $config
            ]);

        $sortedColumns = $this->columnService->getVisibleColumns(true);
        $columnKeys = array_keys($sortedColumns);

        // Should be ordered by the order field: brand_name (1), category_name (3), model_name (5)
        $this->assertEquals(['brand_name', 'category_name', 'model_name'], $columnKeys);
    }

    /** @test */
    public function column_manager_handles_responsive_breakpoints()
    {
        $config = [
            'model_name' => [
                'enabled' => true,
                'required' => true,
                'order' => 1,
                'label' => 'Model',
                'source' => 'model.name',
                'priority' => 1,
                'minBreakpoint' => 'lg'
            ]
        ];

        $this->actingAs($this->admin)
            ->postJson('/admin/parts/compatibility-columns/config', [
                'columns' => $config
            ]);

        $responsiveClass = $this->columnService->getResponsiveClass($config['model_name']);
        $this->assertEquals('hidden lg:table-cell', $responsiveClass);
    }

    /** @test */
    public function column_manager_clears_cache_on_update()
    {
        // Prime the cache
        $this->columnService->getColumnConfiguration();
        $this->assertTrue(Cache::has('compatibility_columns_config'));

        $config = [
            'model_name' => [
                'enabled' => true,
                'required' => true,
                'order' => 1,
                'label' => 'Updated Model',
                'source' => 'model.name',
                'priority' => 1,
                'minBreakpoint' => 'xs'
            ]
        ];

        $this->actingAs($this->admin)
            ->postJson('/admin/parts/compatibility-columns/config', [
                'columns' => $config
            ]);

        // Cache should be cleared
        $this->assertFalse(Cache::has('compatibility_columns_config'));
    }

    /** @test */
    public function non_admin_cannot_access_column_manager_endpoints()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/admin/parts/compatibility-columns/config', [
                'columns' => []
            ]);

        $response->assertForbidden();

        $response = $this->actingAs($this->user)
            ->postJson('/admin/parts/compatibility-columns/toggle', [
                'column' => 'model_name'
            ]);

        $response->assertForbidden();

        $response = $this->actingAs($this->user)
            ->postJson('/admin/parts/compatibility-columns/validate', [
                'columns' => []
            ]);

        $response->assertForbidden();
    }

    /** @test */
    public function column_manager_handles_missing_columns_gracefully()
    {
        $response = $this->actingAs($this->admin)
            ->postJson('/admin/parts/compatibility-columns/toggle', [
                'column' => 'non_existent_column'
            ]);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => 'Column not found'
            ]);
    }
}
