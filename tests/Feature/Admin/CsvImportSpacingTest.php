<?php

namespace Tests\Feature\Admin;

use App\Models\Brand;
use App\Models\MobileModel;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Tests\TestCase;

class CsvImportSpacingTest extends TestCase
{
    use RefreshDatabase;

    protected $admin;
    protected $brand;

    protected function setUp(): void
    {
        parent::setUp();

        $this->admin = User::factory()->create(['role' => 'admin']);
        $this->brand = Brand::factory()->create(['name' => 'Apple']);
    }

    /** @test */
    public function it_handles_model_names_with_leading_spaces_correctly()
    {
        $this->actingAs($this->admin);

        // Create CSV with model name that has leading space
        $csvContent = "Brand,Model Name,Model Number,Release Year,Specifications,Status\n";
        $csvContent .= "Apple,\" iPhone 15\",A2846,2023,Display: 6.1 inch,Active\n";

        $file = UploadedFile::fake()->createWithContent('models.csv', $csvContent);

        $response = $this->post('/admin/bulk-import/models', [
            'file' => $file,
            'duplicate_action' => 'skip'
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify model was created with trimmed name
        $model = MobileModel::where('brand_id', $this->brand->id)->first();
        $this->assertNotNull($model);
        $this->assertEquals('iPhone 15', $model->name); // Should be trimmed
    }

    /** @test */
    public function it_handles_model_names_with_trailing_spaces_correctly()
    {
        $this->actingAs($this->admin);

        // Create CSV with model name that has trailing space
        $csvContent = "Brand,Model Name,Model Number,Release Year,Specifications,Status\n";
        $csvContent .= "Apple,\"iPhone 15 \",A2846,2023,Display: 6.1 inch,Active\n";

        $file = UploadedFile::fake()->createWithContent('models.csv', $csvContent);

        $response = $this->post('/admin/bulk-import/models', [
            'file' => $file,
            'duplicate_action' => 'skip'
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify model was created with trimmed name
        $model = MobileModel::where('brand_id', $this->brand->id)->first();
        $this->assertNotNull($model);
        $this->assertEquals('iPhone 15', $model->name); // Should be trimmed
    }

    /** @test */
    public function it_detects_duplicates_despite_spacing_differences()
    {
        $this->actingAs($this->admin);

        // First import with normal name
        $csvContent1 = "Brand,Model Name,Model Number,Release Year,Specifications,Status\n";
        $csvContent1 .= "Apple,iPhone 15,A2846,2023,Display: 6.1 inch,Active\n";

        $file1 = UploadedFile::fake()->createWithContent('models1.csv', $csvContent1);

        $response1 = $this->post('/admin/bulk-import/models', [
            'file' => $file1,
            'duplicate_action' => 'skip'
        ]);

        $response1->assertRedirect();
        $response1->assertSessionHas('success');

        // Verify first model was created
        $this->assertEquals(1, MobileModel::count());

        // Second import with same name but different spacing
        $csvContent2 = "Brand,Model Name,Model Number,Release Year,Specifications,Status\n";
        $csvContent2 .= "Apple,\" iPhone 15 \",A2847,2024,Display: 6.2 inch,Active\n";

        $file2 = UploadedFile::fake()->createWithContent('models2.csv', $csvContent2);

        $response2 = $this->post('/admin/bulk-import/models', [
            'file' => $file2,
            'duplicate_action' => 'skip'
        ]);

        $response2->assertRedirect();
        $response2->assertSessionHas('success');

        // Should still have only 1 model (duplicate was skipped)
        $this->assertEquals(1, MobileModel::count());

        $successMessage = session('success');
        $this->assertStringContainsString('Skipped 1 duplicate models', $successMessage);
    }

    /** @test */
    public function it_handles_multiple_types_of_whitespace()
    {
        $this->actingAs($this->admin);

        // Create CSV with various types of whitespace
        $csvContent = "Brand,Model Name,Model Number,Release Year,Specifications,Status\n";
        $csvContent .= "Apple,\"\t iPhone 15\u{00A0} \",A2846,2023,Display: 6.1 inch,Active\n"; // Tab, non-breaking space

        $file = UploadedFile::fake()->createWithContent('models.csv', $csvContent);

        $response = $this->post('/admin/bulk-import/models', [
            'file' => $file,
            'duplicate_action' => 'skip'
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify model was created with properly trimmed name
        $model = MobileModel::where('brand_id', $this->brand->id)->first();
        $this->assertNotNull($model);
        $this->assertEquals('iPhone 15', $model->name); // Should be trimmed of all whitespace types
    }

    /** @test */
    public function it_preserves_internal_spaces_while_trimming_edges()
    {
        $this->actingAs($this->admin);

        // Create CSV with model name that has internal spaces
        $csvContent = "Brand,Model Name,Model Number,Release Year,Specifications,Status\n";
        $csvContent .= "Apple,\" iPhone 15 Pro Max \",A2846,2023,Display: 6.1 inch,Active\n";

        $file = UploadedFile::fake()->createWithContent('models.csv', $csvContent);

        $response = $this->post('/admin/bulk-import/models', [
            'file' => $file,
            'duplicate_action' => 'skip'
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify model was created with internal spaces preserved
        $model = MobileModel::where('brand_id', $this->brand->id)->first();
        $this->assertNotNull($model);
        $this->assertEquals('iPhone 15 Pro Max', $model->name); // Internal spaces preserved
    }

    /** @test */
    public function it_handles_brand_names_with_spacing_issues()
    {
        $this->actingAs($this->admin);

        // Create a brand with spacing issues
        $brandWithSpaces = Brand::factory()->create(['name' => ' Samsung ']);

        // Create CSV referencing the brand with different spacing
        $csvContent = "Brand,Model Name,Model Number,Release Year,Specifications,Status\n";
        $csvContent .= "Samsung,Galaxy S24,SM-S921,2024,Display: 6.2 inch,Active\n";

        $file = UploadedFile::fake()->createWithContent('models.csv', $csvContent);

        $response = $this->post('/admin/bulk-import/models', [
            'file' => $file,
            'duplicate_action' => 'skip'
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify model was created and linked to the correct brand
        $model = MobileModel::where('name', 'Galaxy S24')->first();
        $this->assertNotNull($model);
        $this->assertEquals($brandWithSpaces->id, $model->brand_id);
    }

    /** @test */
    public function it_handles_csv_with_bom_and_special_characters()
    {
        $this->actingAs($this->admin);

        // Create CSV with BOM and special characters
        $csvContent = "\xEF\xBB\xBF\"Brand\",\"Model Name\",\"Model Number\",\"Release Year\",\"Specifications\",\"Status\"\n";
        $csvContent .= "Apple,\" iPhone 15\u{200B} \",A2846,2023,\"Display: 6.1 inch\",Active\n"; // Zero-width space

        $file = UploadedFile::fake()->createWithContent('models.csv', $csvContent);

        $response = $this->post('/admin/bulk-import/models', [
            'file' => $file,
            'duplicate_action' => 'skip'
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify model was created with clean name
        $model = MobileModel::where('brand_id', $this->brand->id)->first();
        $this->assertNotNull($model);
        $this->assertEquals('iPhone 15', $model->name); // Should be cleaned
    }

    /** @test */
    public function it_updates_existing_models_with_spacing_normalization()
    {
        $this->actingAs($this->admin);

        // Create existing model
        $existingModel = MobileModel::factory()->create([
            'brand_id' => $this->brand->id,
            'name' => 'iPhone 15',
            'model_number' => 'A2846'
        ]);

        // Import with same model but different spacing and update action
        $csvContent = "Brand,Model Name,Model Number,Release Year,Specifications,Status\n";
        $csvContent .= "Apple,\" iPhone 15 \",A2847,2024,Display: 6.2 inch,Active\n";

        $file = UploadedFile::fake()->createWithContent('models.csv', $csvContent);

        $response = $this->post('/admin/bulk-import/models', [
            'file' => $file,
            'duplicate_action' => 'update'
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Should still have only 1 model
        $this->assertEquals(1, MobileModel::count());

        // Verify model was updated
        $existingModel->refresh();
        $this->assertEquals('A2847', $existingModel->model_number);
        $this->assertEquals(2024, $existingModel->release_year);

        $successMessage = session('success');
        $this->assertStringContainsString('Updated 1 existing models', $successMessage);
    }
}
