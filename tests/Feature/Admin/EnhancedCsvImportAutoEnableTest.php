<?php

namespace Tests\Feature\Admin;

use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use App\Models\User;
use App\Services\CompatibilityColumnService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Tests\TestCase;

class EnhancedCsvImportAutoEnableTest extends TestCase
{
    use RefreshDatabase;

    protected $admin;
    protected $brand;
    protected $category;
    protected $model;
    protected $part;
    protected $columnService;

    protected function setUp(): void
    {
        parent::setUp();

        // Enable export/import functionality for tests
        \App\Models\SiteSetting::updateOrCreate(
            ['key' => 'admin_parts_export_import_enabled'],
            ['value' => '1', 'type' => 'boolean', 'is_active' => true]
        );

        // Clear all related caches
        \Illuminate\Support\Facades\Cache::flush();

        // Create admin user with correct email and admin privileges
        $this->admin = User::factory()->create([
            'email' => '<EMAIL>',
            'is_admin' => true,
            'role' => 'admin',
            'status' => 'active',
            'approval_status' => 'approved'
        ]);

        $this->brand = Brand::factory()->create(['name' => 'TestBrand']);
        $this->category = Category::factory()->create(['name' => 'TestCategory']);
        $this->model = MobileModel::factory()->create([
            'name' => 'TestModel',
            'brand_id' => $this->brand->id,
            'model_number' => 'TM001'
        ]);
        $this->part = Part::factory()->create([
            'name' => 'Test Part',
            'part_number' => 'TP001',
            'category_id' => $this->category->id
        ]);

        $this->columnService = app(CompatibilityColumnService::class);
    }

    /** @test */
    public function it_auto_enables_all_enhanced_csv_columns_with_data()
    {
        $this->actingAs($this->admin);

        // First disable all enhanced columns to test auto-enable functionality
        $initialConfig = $this->columnService->getColumnConfiguration(true);
        $enhancedFields = [
            'front_camera_type', 'camera_position', 'battery_mah',
            'pin_model', 'connector_types', 'types', 'additional_info'
        ];

        // Disable all enhanced fields
        foreach ($enhancedFields as $field) {
            $initialConfig[$field]['enabled'] = false;
        }
        $this->columnService->updateColumnConfiguration($initialConfig);

        // Verify they are disabled
        $disabledConfig = $this->columnService->getColumnConfiguration(true);
        foreach ($enhancedFields as $field) {
            $this->assertFalse($disabledConfig[$field]['enabled'], "Field {$field} should be initially disabled");
        }

        // Create CSV with all enhanced fields
        $csvContent = "ID,Brand,Model,Model Number,Parts Name,Part Number,Description,Manufacturer,Category,Display Types,Display Size,Location,Front Camera Type,Camera Position,Battery mAh,Pin & Model,Connector Types,Types,Compatible,Verified,Notes,Additional Info\n";
        $csvContent .= "{$this->part->id},{$this->brand->name},{$this->model->name},{$this->model->model_number},{$this->part->name},{$this->part->part_number},{$this->part->description},{$this->part->manufacturer},{$this->category->name},AMOLED,6.7 inches,Internal,12MP,Front,4000mAh,USB-C,USB-C,Premium,true,true,Test compatibility notes,Additional test info\n";

        $file = UploadedFile::fake()->createWithContent('enhanced_test.csv', $csvContent);

        $response = $this->post("/admin/parts/{$this->part->id}/compatibility/import", [
            'file' => $file,
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify all enhanced columns are now enabled
        $updatedConfig = $this->columnService->getColumnConfiguration(true);
        foreach ($enhancedFields as $field) {
            $this->assertTrue($updatedConfig[$field]['enabled'], "Field {$field} should be auto-enabled after import");
        }

        // Verify the data was imported correctly
        $compatibility = $this->part->models()->where('model_id', $this->model->id)->first();
        $this->assertNotNull($compatibility);
        $this->assertEquals('12MP', $compatibility->pivot->front_camera_type);
        $this->assertEquals('Front', $compatibility->pivot->camera_position);
        $this->assertEquals('4000mAh', $compatibility->pivot->battery_mah);
        $this->assertEquals('USB-C', $compatibility->pivot->pin_model);
        $this->assertEquals('USB-C', $compatibility->pivot->connector_types);
        $this->assertEquals('Premium', $compatibility->pivot->types);
        $this->assertEquals('Additional test info', $compatibility->pivot->additional_info);
    }

    /** @test */
    public function it_auto_enables_only_columns_with_data()
    {
        $this->actingAs($this->admin);

        // Disable all enhanced columns first
        $initialConfig = $this->columnService->getColumnConfiguration(true);
        $enhancedFields = [
            'front_camera_type', 'camera_position', 'battery_mah',
            'pin_model', 'connector_types', 'types', 'additional_info'
        ];

        foreach ($enhancedFields as $field) {
            $initialConfig[$field]['enabled'] = false;
        }
        $this->columnService->updateColumnConfiguration($initialConfig);

        // Create CSV with only some enhanced fields
        $csvContent = "ID,Brand,Model,Front Camera Type,Battery mAh,Compatible,Verified\n";
        $csvContent .= "{$this->part->id},{$this->brand->name},{$this->model->name},8MP,3000mAh,true,true\n";

        $file = UploadedFile::fake()->createWithContent('partial_test.csv', $csvContent);

        $response = $this->post("/admin/parts/{$this->part->id}/compatibility/import", [
            'file' => $file,
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify only columns with data are enabled
        $updatedConfig = $this->columnService->getColumnConfiguration(true);
        $this->assertTrue($updatedConfig['front_camera_type']['enabled']);
        $this->assertTrue($updatedConfig['battery_mah']['enabled']);
        
        // These should remain disabled as they weren't in the CSV
        $this->assertFalse($updatedConfig['camera_position']['enabled']);
        $this->assertFalse($updatedConfig['pin_model']['enabled']);
        $this->assertFalse($updatedConfig['connector_types']['enabled']);
        $this->assertFalse($updatedConfig['types']['enabled']);
        $this->assertFalse($updatedConfig['additional_info']['enabled']);
    }

    /** @test */
    public function it_maintains_backward_compatibility_with_old_csv_format()
    {
        $this->actingAs($this->admin);

        // Create CSV with old format (no enhanced fields)
        $csvContent = "Brand,Model,Model Number,Compatible,Verified,Display Type,Display Size,Location,Notes\n";
        $csvContent .= "{$this->brand->name},{$this->model->name},{$this->model->model_number},true,true,OLED,6.1 inches,Front,Test notes\n";

        $file = UploadedFile::fake()->createWithContent('old_format_test.csv', $csvContent);

        $response = $this->post("/admin/parts/{$this->part->id}/compatibility/import", [
            'file' => $file,
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify old format columns are enabled
        $updatedConfig = $this->columnService->getColumnConfiguration(true);
        $this->assertTrue($updatedConfig['display_type']['enabled']);
        $this->assertTrue($updatedConfig['display_size']['enabled']);
        $this->assertTrue($updatedConfig['location']['enabled']);

        // Verify enhanced columns remain disabled
        $enhancedFields = [
            'front_camera_type', 'camera_position', 'battery_mah', 
            'pin_model', 'connector_types', 'types', 'additional_info'
        ];
        
        foreach ($enhancedFields as $field) {
            $this->assertFalse($updatedConfig[$field]['enabled'], "Enhanced field {$field} should remain disabled");
        }

        // Verify the data was imported correctly
        $compatibility = $this->part->models()->where('model_id', $this->model->id)->first();
        $this->assertNotNull($compatibility);
        $this->assertEquals('OLED', $compatibility->pivot->display_type);
        $this->assertEquals('6.1 inches', $compatibility->pivot->display_size);
        $this->assertEquals('Front', $compatibility->pivot->location);
        $this->assertEquals('Test notes', $compatibility->pivot->compatibility_notes);
    }

    /** @test */
    public function it_handles_mixed_old_and_new_format_columns()
    {
        $this->actingAs($this->admin);

        // Create CSV with mix of old and new format columns
        $csvContent = "Brand,Model,Display Type,Front Camera Type,Battery mAh,Compatible,Additional Info\n";
        $csvContent .= "{$this->brand->name},{$this->model->name},LCD,16MP,5000mAh,true,Mixed format test\n";

        $file = UploadedFile::fake()->createWithContent('mixed_format_test.csv', $csvContent);

        $response = $this->post("/admin/parts/{$this->part->id}/compatibility/import", [
            'file' => $file,
        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify both old and new columns are enabled
        $updatedConfig = $this->columnService->getColumnConfiguration(true);
        $this->assertTrue($updatedConfig['display_type']['enabled']);
        $this->assertTrue($updatedConfig['front_camera_type']['enabled']);
        $this->assertTrue($updatedConfig['battery_mah']['enabled']);
        $this->assertTrue($updatedConfig['additional_info']['enabled']);

        // Verify the data was imported correctly
        $compatibility = $this->part->models()->where('model_id', $this->model->id)->first();
        $this->assertNotNull($compatibility);
        $this->assertEquals('LCD', $compatibility->pivot->display_type);
        $this->assertEquals('16MP', $compatibility->pivot->front_camera_type);
        $this->assertEquals('5000mAh', $compatibility->pivot->battery_mah);
        $this->assertEquals('Mixed format test', $compatibility->pivot->additional_info);
    }
}
