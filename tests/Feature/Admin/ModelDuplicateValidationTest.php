<?php

namespace Tests\Feature\Admin;

use App\Models\Brand;
use App\Models\MobileModel;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ModelDuplicateValidationTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected Brand $brand;

    protected function setUp(): void
    {
        parent::setUp();

        // Create an admin user for authentication
        $this->user = $this->createAdminUser();
        $this->actingAs($this->user);

        // Create a brand for testing
        $this->brand = Brand::factory()->create(['name' => 'Test Brand']);
    }

    public function test_can_create_model_with_unique_name_within_brand()
    {
        $modelData = [
            'brand_id' => $this->brand->id,
            'name' => 'Unique Model',
            'model_number' => 'UM-001',
            'release_year' => 2024,
            'is_active' => true,
        ];

        $response = $this->postWithCsrf('/admin/models', $modelData);

        $response->assertRedirect('/admin/models');
        $response->assertSessionHas('success', 'Model created successfully.');
        $this->assertDatabaseHas('models', [
            'name' => 'Unique Model',
            'brand_id' => $this->brand->id,
        ]);
    }

    public function test_cannot_create_duplicate_model_name_within_same_brand()
    {
        // Create an existing model
        MobileModel::factory()->create([
            'brand_id' => $this->brand->id,
            'name' => 'Duplicate Model',
        ]);

        // Try to create another model with the same name in the same brand
        $modelData = [
            'brand_id' => $this->brand->id,
            'name' => 'Duplicate Model',
            'model_number' => 'DM-002',
            'release_year' => 2024,
            'is_active' => true,
        ];

        $response = $this->postWithCsrf('/admin/models', $modelData);

        $response->assertRedirect();
        $response->assertSessionHasErrors(['name']);
        $this->assertEquals(
            'The name has already been taken.',
            session('errors')->get('name')[0]
        );

        // Ensure only one model exists with this name
        $this->assertEquals(1, MobileModel::where('name', 'Duplicate Model')->count());
    }

    public function test_can_create_same_model_name_in_different_brands()
    {
        $anotherBrand = Brand::factory()->create(['name' => 'Another Brand']);

        // Create a model in the first brand
        MobileModel::factory()->create([
            'brand_id' => $this->brand->id,
            'name' => 'Same Name Model',
        ]);

        // Create a model with the same name in a different brand
        $modelData = [
            'brand_id' => $anotherBrand->id,
            'name' => 'Same Name Model',
            'model_number' => 'SNM-002',
            'release_year' => 2024,
            'is_active' => true,
        ];

        $response = $this->postWithCsrf('/admin/models', $modelData);

        $response->assertRedirect('/admin/models');
        $response->assertSessionHas('success', 'Model created successfully.');

        // Ensure both models exist
        $this->assertEquals(2, MobileModel::where('name', 'Same Name Model')->count());
        $this->assertDatabaseHas('models', [
            'name' => 'Same Name Model',
            'brand_id' => $this->brand->id,
        ]);
        $this->assertDatabaseHas('models', [
            'name' => 'Same Name Model',
            'brand_id' => $anotherBrand->id,
        ]);
    }

    public function test_can_update_model_with_same_name_when_not_changing_brand()
    {
        $model = MobileModel::factory()->create([
            'brand_id' => $this->brand->id,
            'name' => 'Original Model',
        ]);

        $updateData = [
            'brand_id' => $this->brand->id,
            'name' => 'Original Model', // Same name
            'model_number' => 'OM-UPDATED',
            'release_year' => 2025,
            'is_active' => true,
        ];

        $response = $this->putWithCsrf("/admin/models/{$model->id}", $updateData);

        $response->assertRedirect('/admin/models');
        $response->assertSessionHas('success', 'Model updated successfully.');
        $this->assertDatabaseHas('models', [
            'id' => $model->id,
            'name' => 'Original Model',
            'model_number' => 'OM-UPDATED',
        ]);
    }

    public function test_cannot_update_model_to_duplicate_name_within_same_brand()
    {
        // Create two models in the same brand
        $existingModel = MobileModel::factory()->create([
            'brand_id' => $this->brand->id,
            'name' => 'Existing Model',
        ]);

        $modelToUpdate = MobileModel::factory()->create([
            'brand_id' => $this->brand->id,
            'name' => 'Model To Update',
        ]);

        // Try to update the second model to have the same name as the first
        $updateData = [
            'brand_id' => $this->brand->id,
            'name' => 'Existing Model', // Duplicate name
            'model_number' => 'MTU-001',
            'release_year' => 2024,
            'is_active' => true,
        ];

        $response = $this->putWithCsrf("/admin/models/{$modelToUpdate->id}", $updateData);

        $response->assertRedirect();
        $response->assertSessionHasErrors(['name']);
        $this->assertEquals(
            'The name has already been taken.',
            session('errors')->get('name')[0]
        );

        // Ensure the model wasn't updated
        $this->assertDatabaseHas('models', [
            'id' => $modelToUpdate->id,
            'name' => 'Model To Update', // Original name preserved
        ]);
    }

    public function test_can_update_model_to_same_name_in_different_brand()
    {
        $anotherBrand = Brand::factory()->create(['name' => 'Another Brand']);

        // Create a model in the first brand
        MobileModel::factory()->create([
            'brand_id' => $this->brand->id,
            'name' => 'Shared Name',
        ]);

        // Create a model in the second brand
        $modelToUpdate = MobileModel::factory()->create([
            'brand_id' => $anotherBrand->id,
            'name' => 'Original Name',
        ]);

        // Update the second model to have the same name as the first (but different brand)
        $updateData = [
            'brand_id' => $anotherBrand->id,
            'name' => 'Shared Name',
            'model_number' => 'SN-002',
            'release_year' => 2024,
            'is_active' => true,
        ];

        $response = $this->putWithCsrf("/admin/models/{$modelToUpdate->id}", $updateData);

        $response->assertRedirect('/admin/models');
        $response->assertSessionHas('success', 'Model updated successfully.');

        // Ensure both models exist with the same name but different brands
        $this->assertEquals(2, MobileModel::where('name', 'Shared Name')->count());
        $this->assertDatabaseHas('models', [
            'id' => $modelToUpdate->id,
            'name' => 'Shared Name',
            'brand_id' => $anotherBrand->id,
        ]);
    }

    public function test_validation_error_message_is_user_friendly()
    {
        // Create an existing model
        MobileModel::factory()->create([
            'brand_id' => $this->brand->id,
            'name' => 'Test Model',
        ]);

        // Try to create a duplicate
        $modelData = [
            'brand_id' => $this->brand->id,
            'name' => 'Test Model',
            'is_active' => true,
        ];

        $response = $this->postWithCsrf('/admin/models', $modelData);

        $response->assertRedirect();
        $response->assertSessionHasErrors(['name']);
        
        // Check that the error message is user-friendly
        $errors = session('errors');
        $nameError = $errors->get('name')[0];
        $this->assertStringContainsString('already been taken', $nameError);
    }

    public function test_case_sensitive_duplicate_validation()
    {
        // Create a model with lowercase name
        MobileModel::factory()->create([
            'brand_id' => $this->brand->id,
            'name' => 'test model',
        ]);

        // Try to create a model with different case
        $modelData = [
            'brand_id' => $this->brand->id,
            'name' => 'Test Model',
            'is_active' => true,
        ];

        $response = $this->postWithCsrf('/admin/models', $modelData);

        // Should succeed as validation is case-sensitive
        $response->assertRedirect('/admin/models');
        $response->assertSessionHas('success', 'Model created successfully.');
        $this->assertEquals(2, MobileModel::where('brand_id', $this->brand->id)->count());
    }

    public function test_whitespace_handling_in_duplicate_validation()
    {
        // Create a model with name containing spaces
        MobileModel::factory()->create([
            'brand_id' => $this->brand->id,
            'name' => 'Test Model',
        ]);

        // Try to create a model with extra whitespace - should fail due to duplicate after trimming
        $modelData = [
            'brand_id' => $this->brand->id,
            'name' => ' Test Model ',
            'is_active' => true,
        ];

        $response = $this->postWithCsrf('/admin/models', $modelData);

        // Should fail because after trimming, it becomes a duplicate
        $response->assertRedirect();
        $response->assertSessionHasErrors(['name']);
    }
}
