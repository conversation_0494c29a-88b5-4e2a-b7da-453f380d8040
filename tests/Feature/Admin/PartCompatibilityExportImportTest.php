<?php

namespace Tests\Feature\Admin;

use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use App\Models\SiteSetting;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PartCompatibilityExportImportTest extends TestCase
{
    use RefreshDatabase;

    private User $admin;
    private Part $part;
    private MobileModel $model;

    protected function setUp(): void
    {
        parent::setUp();

        $this->admin = User::factory()->create(['role' => 'admin']);
        
        // Create test data
        $brand = Brand::factory()->create(['name' => 'Apple']);
        $category = Category::factory()->create(['name' => 'Display']);
        
        $this->part = Part::factory()->create([
            'name' => 'LCD Screen',
            'part_number' => 'LCD-001',
            'description' => 'High quality LCD screen',
            'manufacturer' => 'Samsung',
            'category_id' => $category->id,
        ]);

        $this->model = MobileModel::factory()->create([
            'name' => 'iPhone 13',
            'model_number' => 'A2482',
            'brand_id' => $brand->id,
        ]);

        // Attach model to part with pivot data
        $this->part->models()->attach($this->model->id, [
            'display_type' => 'OLED',
            'display_size' => '6.1 inches',
            'location' => 'Front',
            'is_verified' => true,
            'is_compatible' => true,
            'compatibility_notes' => 'Fully compatible',
        ]);
    }

    /** @test */
    public function show_page_passes_export_import_enabled_setting_when_enabled()
    {
        // Enable export functionality
        SiteSetting::set('admin_parts_export_import_enabled', true);

        $response = $this->actingAs($this->admin)
            ->get("/admin/parts/{$this->part->id}");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/Parts/Show')
                ->has('exportImportEnabled')
                ->where('exportImportEnabled', true)
        );
    }

    /** @test */
    public function show_page_passes_export_import_enabled_setting_when_disabled()
    {
        // Disable export functionality
        SiteSetting::set('admin_parts_export_import_enabled', false);

        $response = $this->actingAs($this->admin)
            ->get("/admin/parts/{$this->part->id}");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/Parts/Show')
                ->has('exportImportEnabled')
                ->where('exportImportEnabled', false)
        );
    }

    /** @test */
    public function compatibility_page_passes_export_import_enabled_setting_when_enabled()
    {
        // Enable export functionality
        SiteSetting::set('admin_parts_export_import_enabled', true);

        $response = $this->actingAs($this->admin)
            ->get("/admin/parts/{$this->part->id}/compatibility");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/Parts/Compatibility')
                ->has('exportImportEnabled')
                ->where('exportImportEnabled', true)
        );
    }

    /** @test */
    public function compatibility_page_passes_export_import_enabled_setting_when_disabled()
    {
        // Disable export functionality
        SiteSetting::set('admin_parts_export_import_enabled', false);

        $response = $this->actingAs($this->admin)
            ->get("/admin/parts/{$this->part->id}/compatibility");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/Parts/Compatibility')
                ->has('exportImportEnabled')
                ->where('exportImportEnabled', false)
        );
    }

    /** @test */
    public function edit_compatibility_page_passes_export_import_enabled_setting_when_enabled()
    {
        // Enable export functionality
        SiteSetting::set('admin_parts_export_import_enabled', true);

        $response = $this->actingAs($this->admin)
            ->get("/admin/parts/{$this->part->id}/compatibility/edit");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/Parts/EditCompatibility')
                ->has('exportImportEnabled')
                ->where('exportImportEnabled', true)
        );
    }

    /** @test */
    public function edit_compatibility_page_passes_export_import_enabled_setting_when_disabled()
    {
        // Disable export functionality
        SiteSetting::set('admin_parts_export_import_enabled', false);

        $response = $this->actingAs($this->admin)
            ->get("/admin/parts/{$this->part->id}/compatibility/edit");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('admin/Parts/EditCompatibility')
                ->has('exportImportEnabled')
                ->where('exportImportEnabled', false)
        );
    }

    /** @test */
    public function export_single_part_blocked_when_setting_disabled()
    {
        // Disable export functionality
        SiteSetting::set('admin_parts_export_import_enabled', false);

        $response = $this->actingAs($this->admin)
            ->get("/admin/parts/{$this->part->id}/export");

        $response->assertStatus(403);
        $response->assertJson(['error' => 'This feature is disabled for security reasons.']);
    }

    /** @test */
    public function export_single_part_works_when_setting_enabled()
    {
        // Enable export functionality
        SiteSetting::set('admin_parts_export_import_enabled', true);

        $response = $this->actingAs($this->admin)
            ->get("/admin/parts/{$this->part->id}/export");

        $response->assertStatus(200);
        $response->assertHeader('content-type', 'text/csv; charset=UTF-8');
    }

    /** @test */
    public function download_compatibility_template_blocked_when_setting_disabled()
    {
        // Disable export functionality
        SiteSetting::set('admin_parts_export_import_enabled', false);

        $response = $this->actingAs($this->admin)
            ->get("/admin/parts/{$this->part->id}/compatibility/template");

        $response->assertStatus(403);
        $response->assertJson(['error' => 'This feature is disabled for security reasons.']);
    }

    /** @test */
    public function download_compatibility_template_works_when_setting_enabled()
    {
        // Enable export functionality
        SiteSetting::set('admin_parts_export_import_enabled', true);

        $response = $this->actingAs($this->admin)
            ->get("/admin/parts/{$this->part->id}/compatibility/template");

        $response->assertStatus(200);
        $response->assertHeader('content-type', 'text/csv; charset=UTF-8');
    }

    /** @test */
    public function import_compatibility_preview_blocked_when_setting_disabled()
    {
        // Disable export functionality
        SiteSetting::set('admin_parts_export_import_enabled', false);

        // Create a simple CSV file for testing
        $csvContent = "Brand,Model,Compatible\nApple,iPhone 13,true";
        $tempFile = tmpfile();
        fwrite($tempFile, $csvContent);
        $tempPath = stream_get_meta_data($tempFile)['uri'];

        $response = $this->actingAs($this->admin)
            ->post("/admin/parts/{$this->part->id}/compatibility/preview", [
                'file' => new \Illuminate\Http\UploadedFile($tempPath, 'test.csv', 'text/csv', null, true)
            ]);

        $response->assertStatus(403);
        $response->assertJson(['error' => 'This feature is disabled for security reasons.']);

        fclose($tempFile);
    }

    /** @test */
    public function import_compatibility_blocked_when_setting_disabled()
    {
        // Disable export functionality
        SiteSetting::set('admin_parts_export_import_enabled', false);

        // Create a simple CSV file for testing
        $csvContent = "Brand,Model,Compatible\nApple,iPhone 13,true";
        $tempFile = tmpfile();
        fwrite($tempFile, $csvContent);
        $tempPath = stream_get_meta_data($tempFile)['uri'];

        $response = $this->actingAs($this->admin)
            ->post("/admin/parts/{$this->part->id}/compatibility/import", [
                'file' => new \Illuminate\Http\UploadedFile($tempPath, 'test.csv', 'text/csv', null, true),
                'selected_columns' => ['brand', 'model', 'compatible']
            ]);

        $response->assertStatus(403);
        $response->assertJson(['error' => 'This feature is disabled for security reasons.']);

        fclose($tempFile);
    }


}
