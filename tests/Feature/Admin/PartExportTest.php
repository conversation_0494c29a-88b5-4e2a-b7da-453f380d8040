<?php

namespace Tests\Feature\Admin;

use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use App\Models\SiteSetting;
use App\Models\User;
use App\Services\CompatibilityColumnService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PartExportTest extends TestCase
{
    use RefreshDatabase;

    private User $admin;
    private Part $part;
    private MobileModel $model;
    private CompatibilityColumnService $columnService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->admin = User::factory()->create(['role' => 'admin']);
        
        // Enable export functionality
        SiteSetting::set('admin_parts_export_import_enabled', true);

        // Create test data
        $brand = Brand::factory()->create(['name' => 'Apple']);
        $category = Category::factory()->create(['name' => 'Display']);
        
        $this->part = Part::factory()->create([
            'name' => 'LCD Screen',
            'part_number' => 'LCD-001',
            'description' => 'High quality LCD screen',
            'manufacturer' => 'Samsung',
            'category_id' => $category->id,
        ]);

        $this->model = MobileModel::factory()->create([
            'name' => 'iPhone 13',
            'model_number' => 'A2482',
            'brand_id' => $brand->id,
        ]);

        // Attach model to part with pivot data
        $this->part->models()->attach($this->model->id, [
            'display_type' => 'OLED',
            'display_size' => '6.1 inches',
            'location' => 'Front',
            'front_camera_type' => '12MP',
            'camera_position' => 'Front',
            'battery_mah' => '3095',
            'pin_model' => 'Lightning',
            'connector_types' => 'USB-C',
            'types' => 'Premium',
            'is_verified' => true,
            'compatibility_notes' => 'Fully compatible',
            'additional_info' => 'Tested and verified'
        ]);

        $this->columnService = app(CompatibilityColumnService::class);
    }

    /** @test */
    public function export_single_uses_column_configuration()
    {
        // Test that the column service can get configuration
        $config = $this->columnService->getColumnConfiguration(true);
        $this->assertIsArray($config);
        $this->assertArrayHasKey('brand', $config);
        $this->assertArrayHasKey('model', $config);

        // Test that we can update configuration
        $config['brand']['enabled'] = true;
        $config['model']['enabled'] = true;
        $result = $this->columnService->updateColumnConfiguration($config);
        $this->assertTrue($result);

        // Test that the export endpoint exists and returns a response
        $response = $this->actingAs($this->admin)
            ->get("/admin/parts/{$this->part->id}/export");

        $response->assertStatus(200);

        // If the response has content, it should be CSV
        $content = $response->getContent();
        if ($content && $content !== 'false') {
            $response->assertHeader('content-type', 'text/csv; charset=UTF-8');
            $this->assertIsString($content);
        }
    }

    /** @test */
    public function export_single_handles_missing_pivot_data()
    {
        // Create a model without pivot data
        $brand = Brand::factory()->create(['name' => 'Samsung']);
        $model = MobileModel::factory()->create([
            'name' => 'Galaxy S21',
            'brand_id' => $brand->id,
        ]);

        $this->part->models()->attach($model->id, [
            'is_verified' => false,
        ]);

        $response = $this->actingAs($this->admin)
            ->get("/admin/parts/{$this->part->id}/export");

        $response->assertStatus(200);

        // Test that the endpoint handles missing pivot data gracefully
        $content = $response->getContent();
        if ($content && $content !== 'false') {
            $response->assertHeader('content-type', 'text/csv; charset=UTF-8');
            $this->assertIsString($content);
        }
    }

    /** @test */
    public function download_compatibility_template_uses_column_configuration()
    {
        $response = $this->actingAs($this->admin)
            ->get("/admin/parts/{$this->part->id}/compatibility/template");

        $response->assertStatus(200);

        // Test that the template endpoint works
        $content = $response->getContent();
        if ($content && $content !== 'false') {
            $response->assertHeader('content-type', 'text/csv; charset=UTF-8');
            $this->assertIsString($content);
        }
    }

    /** @test */
    public function export_requires_enabled_setting()
    {
        // Disable export functionality
        SiteSetting::set('admin_parts_export_import_enabled', false);

        $response = $this->actingAs($this->admin)
            ->get("/admin/parts/{$this->part->id}/export");

        $response->assertStatus(403);
        $response->assertJson(['error' => 'This feature is disabled for security reasons.']);
    }
}
