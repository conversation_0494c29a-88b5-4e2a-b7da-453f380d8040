<?php

namespace Tests\Feature;

use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use App\Models\User;
use App\Models\UserFavorite;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class CleanModelsPartsDataCommandTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create necessary dependencies for testing
        $this->createTestData();
    }

    public function test_it_shows_current_stats_when_no_data_exists()
    {
        // Clean up any existing data first
        MobileModel::query()->delete();
        Part::query()->delete();
        DB::table('model_parts')->delete();
        UserFavorite::query()->delete();

        $this->artisan('catalog:clean-all --force')
            ->expectsOutput('✅ No models or parts data found. Nothing to clean.')
            ->assertExitCode(0);
    }

    public function test_it_displays_current_statistics_correctly()
    {
        $this->artisan('catalog:clean-all --dry-run')
            ->expectsTable(['Data Type', 'Count'], [
                ['Models', '3'],
                ['Parts', '3'],
                ['Model-Part Relationships', '3'],
                ['User Favorites (Models)', '1'],
                ['User Favorites (Parts)', '1'],
            ])
            ->assertExitCode(0);
    }

    public function test_it_shows_what_will_be_deleted_in_dry_run_mode()
    {
        $this->artisan('catalog:clean-all --dry-run')
            ->expectsOutput('🔍 DRY RUN MODE - No data will be deleted')
            ->expectsOutput('🗑️  The following data would be deleted:')
            ->expectsOutput('   • 1 user favorite records (models)')
            ->expectsOutput('   • 1 user favorite records (parts)')
            ->expectsOutput('   • 3 model-part relationship records')
            ->expectsOutput('   • 3 parts records')
            ->expectsOutput('   • 3 models records')
            ->assertExitCode(0);
    }

    public function test_it_cancels_operation_when_user_declines_confirmation()
    {
        $this->artisan('catalog:clean-all')
            ->expectsQuestion('Are you sure you want to proceed?', false)
            ->expectsOutput('❌ Operation cancelled.')
            ->assertExitCode(1);

        // Verify data is still there
        $this->assertDatabaseCount('models', 3);
        $this->assertDatabaseCount('parts', 3);
        $this->assertDatabaseCount('model_parts', 3);
        $this->assertEquals(1, UserFavorite::where('favoritable_type', 'App\Models\MobileModel')->count());
        $this->assertEquals(1, UserFavorite::where('favoritable_type', 'App\Models\Part')->count());
    }

    public function test_it_successfully_cleans_all_models_and_parts_data_with_force_flag()
    {
        // Get initial auto-increment values
        $initialModelsAutoIncrement = $this->getCurrentAutoIncrement('models');
        $initialPartsAutoIncrement = $this->getCurrentAutoIncrement('parts');
        
        $this->assertGreaterThan(1, $initialModelsAutoIncrement);
        $this->assertGreaterThan(1, $initialPartsAutoIncrement);

        $this->artisan('catalog:clean-all --force')
            ->expectsOutput('🚀 Starting cleanup process...')
            ->expectsOutput('🗑️  Deleting user favorites (models)...')
            ->expectsOutput('🗑️  Deleting user favorites (parts)...')
            ->expectsOutput('🗑️  Deleting model-part relationships...')
            ->expectsOutput('🗑️  Deleting parts...')
            ->expectsOutput('🗑️  Deleting models...')
            ->expectsOutput('🔄 Resetting AUTO_INCREMENT values...')
            ->expectsOutput('✅ Cleanup completed successfully!')
            ->expectsOutput('🎉 Next models and parts created will have ID: 1')
            ->assertExitCode(0);

        // Verify all data is deleted
        $this->assertDatabaseCount('models', 0);
        $this->assertDatabaseCount('parts', 0);
        $this->assertDatabaseCount('model_parts', 0);
        $this->assertEquals(0, UserFavorite::where('favoritable_type', 'App\Models\MobileModel')->count());
        $this->assertEquals(0, UserFavorite::where('favoritable_type', 'App\Models\Part')->count());

        // Verify auto-increment is reset
        $newModelsAutoIncrement = $this->getCurrentAutoIncrement('models');
        $newPartsAutoIncrement = $this->getCurrentAutoIncrement('parts');
        $this->assertEquals(1, $newModelsAutoIncrement);
        $this->assertEquals(1, $newPartsAutoIncrement);
    }

    public function test_it_creates_new_model_and_part_with_id_1_after_cleanup()
    {
        // Clean all data
        $this->artisan('catalog:clean-all --force');

        // Create a new model
        $brand = Brand::first();
        $newModel = MobileModel::create([
            'brand_id' => $brand->id,
            'name' => 'Test Model After Cleanup',
            'is_active' => true,
        ]);

        // Create a new part
        $category = Category::first();
        $newPart = Part::create([
            'category_id' => $category->id,
            'name' => 'Test Part After Cleanup',
            'is_active' => true,
        ]);

        // Verify both have ID 1
        $this->assertEquals(1, $newModel->id);
        $this->assertEquals(1, $newPart->id);
        
        $this->assertDatabaseHas('models', [
            'id' => 1,
            'name' => 'Test Model After Cleanup',
        ]);
        
        $this->assertDatabaseHas('parts', [
            'id' => 1,
            'name' => 'Test Part After Cleanup',
        ]);
    }

    public function test_it_cleans_only_models_when_models_only_flag_is_used()
    {
        $this->artisan('catalog:clean-all --models-only --force')
            ->expectsOutput('✅ Cleanup completed successfully!')
            ->assertExitCode(0);

        // Verify only models are deleted, parts remain
        $this->assertDatabaseCount('models', 0);
        $this->assertDatabaseCount('parts', 3);
        $this->assertDatabaseCount('model_parts', 0); // Should be deleted due to FK cascade
    }

    public function test_it_cleans_only_parts_when_parts_only_flag_is_used()
    {
        $this->artisan('catalog:clean-all --parts-only --force')
            ->expectsOutput('✅ Cleanup completed successfully!')
            ->assertExitCode(0);

        // Verify only parts are deleted, models remain
        $this->assertDatabaseCount('models', 3);
        $this->assertDatabaseCount('parts', 0);
        $this->assertDatabaseCount('model_parts', 0); // Should be deleted due to FK cascade
    }

    public function test_it_maintains_referential_integrity_during_cleanup()
    {
        // Verify initial state has relationships
        $this->assertDatabaseCount('model_parts', 3);
        
        $this->artisan('catalog:clean-all --force');

        // Verify all related data is properly cleaned
        $this->assertDatabaseCount('models', 0);
        $this->assertDatabaseCount('parts', 0);
        $this->assertDatabaseCount('model_parts', 0);
        
        // Verify other tables are not affected
        $this->assertDatabaseCount('categories', 3);
        $this->assertDatabaseCount('brands', 3);
        $this->assertDatabaseCount('users', 2);
    }

    public function test_it_handles_cleanup_with_confirmation()
    {
        $this->artisan('catalog:clean-all')
            ->expectsQuestion('Are you sure you want to proceed?', true)
            ->expectsOutput('✅ Cleanup completed successfully!')
            ->assertExitCode(0);

        // Verify cleanup was successful
        $this->assertDatabaseCount('models', 0);
        $this->assertDatabaseCount('parts', 0);
        $this->assertDatabaseCount('model_parts', 0);
    }

    /**
     * Create test data for the tests.
     */
    private function createTestData(): void
    {
        // Create brands and categories
        $brands = Brand::factory(3)->create();
        $categories = Category::factory(3)->create();

        // Create models
        $models = collect();
        foreach ($brands as $brand) {
            $models->push(MobileModel::factory()->create(['brand_id' => $brand->id]));
        }

        // Create parts
        $parts = collect();
        foreach ($categories as $category) {
            $parts->push(Part::factory()->create(['category_id' => $category->id]));
        }

        // Create model-part relationships
        foreach ($models as $index => $model) {
            $model->parts()->attach($parts[$index]->id, [
                'compatibility_notes' => 'Test compatibility',
                'is_verified' => true,
            ]);
        }

        // Create users and user favorites
        $users = User::factory(2)->create();
        
        // Create one favorite for model and one for part
        UserFavorite::create([
            'user_id' => $users[0]->id,
            'favoritable_type' => 'App\Models\MobileModel',
            'favoritable_id' => $models[0]->id,
        ]);

        UserFavorite::create([
            'user_id' => $users[1]->id,
            'favoritable_type' => 'App\Models\Part',
            'favoritable_id' => $parts[0]->id,
        ]);
    }

    /**
     * Get current AUTO_INCREMENT value for specified table.
     */
    private function getCurrentAutoIncrement(string $table): int
    {
        $driver = DB::getDriverName();
        
        if ($driver === 'mysql') {
            $result = DB::select("SELECT AUTO_INCREMENT FROM information_schema.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '{$table}'");
            return $result[0]->AUTO_INCREMENT ?? 1;
        } elseif ($driver === 'sqlite') {
            $result = DB::select("SELECT seq FROM sqlite_sequence WHERE name = '{$table}'");
            return isset($result[0]) ? $result[0]->seq + 1 : 1;
        } else {
            return 1;
        }
    }
}
