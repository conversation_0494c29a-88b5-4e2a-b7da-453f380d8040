<?php

namespace Tests\Feature;

use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use App\Models\User;
use App\Models\UserFavorite;
use App\Models\Brand;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class CleanPartsDataCommandTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create necessary dependencies for testing
        $this->createTestData();
    }

    /** @test */
    public function it_shows_current_stats_when_no_data_exists()
    {
        // Clean up any existing data first
        Part::query()->delete();
        DB::table('model_parts')->delete();
        UserFavorite::where('favoritable_type', 'App\Models\Part')->delete();

        $this->artisan('parts:clean-all --force')
            ->expectsOutput('✅ No parts-related data found. Nothing to clean.')
            ->assertExitCode(0);
    }

    /** @test */
    public function it_displays_current_statistics_correctly()
    {
        $this->artisan('parts:clean-all --dry-run')
            ->expectsTable(['Data Type', 'Count'], [
                ['Parts', '3'],
                ['Model-Part Relationships', '3'],
                ['User Favorites (Parts)', '2'],
            ])
            ->assertExitCode(0);
    }

    /** @test */
    public function it_shows_what_will_be_deleted_in_dry_run_mode()
    {
        $this->artisan('parts:clean-all --dry-run')
            ->expectsOutput('🔍 DRY RUN MODE - No data will be deleted')
            ->expectsOutput('🗑️  The following data would be deleted:')
            ->expectsOutput('   • 3 parts records')
            ->expectsOutput('   • 3 model-part relationship records')
            ->expectsOutput('   • 2 user favorite records')
            ->assertExitCode(0);
    }

    /** @test */
    public function it_cancels_operation_when_user_declines_confirmation()
    {
        $this->artisan('parts:clean-all')
            ->expectsQuestion('Are you sure you want to proceed?', false)
            ->expectsOutput('❌ Operation cancelled.')
            ->assertExitCode(1);

        // Verify data is still there
        $this->assertDatabaseCount('parts', 3);
        $this->assertDatabaseCount('model_parts', 3);
        $this->assertEquals(2, UserFavorite::where('favoritable_type', 'App\Models\Part')->count());
    }

    /** @test */
    public function it_successfully_cleans_all_parts_data_with_force_flag()
    {
        // Get initial auto-increment value
        $initialAutoIncrement = $this->getCurrentAutoIncrement();
        $this->assertGreaterThan(1, $initialAutoIncrement);

        $this->artisan('parts:clean-all --force')
            ->expectsOutput('🚀 Starting cleanup process...')
            ->expectsOutput('🗑️  Deleting user favorites...')
            ->expectsOutput('🗑️  Deleting model-part relationships...')
            ->expectsOutput('🗑️  Deleting parts...')
            ->expectsOutput('🔄 Resetting AUTO_INCREMENT...')
            ->expectsOutput('✅ Cleanup completed successfully!')
            ->expectsOutput('🎉 Next part created will have ID: 1')
            ->assertExitCode(0);

        // Verify all data is deleted
        $this->assertDatabaseCount('parts', 0);
        $this->assertDatabaseCount('model_parts', 0);
        $this->assertEquals(0, UserFavorite::where('favoritable_type', 'App\Models\Part')->count());

        // Verify auto-increment is reset
        $newAutoIncrement = $this->getCurrentAutoIncrement();
        $this->assertEquals(1, $newAutoIncrement);
    }

    /** @test */
    public function it_creates_new_part_with_id_1_after_cleanup()
    {
        // Clean all data
        $this->artisan('parts:clean-all --force');

        // Create a new part
        $category = Category::first();
        $newPart = Part::create([
            'category_id' => $category->id,
            'name' => 'Test Part After Cleanup',
            'description' => 'This should have ID 1',
            'is_active' => true,
        ]);

        // Verify the new part has ID 1
        $this->assertEquals(1, $newPart->id);
        $this->assertDatabaseHas('parts', [
            'id' => 1,
            'name' => 'Test Part After Cleanup',
        ]);
    }

    /** @test */
    public function it_handles_cleanup_with_confirmation()
    {
        $this->artisan('parts:clean-all')
            ->expectsQuestion('Are you sure you want to proceed?', true)
            ->expectsOutput('✅ Cleanup completed successfully!')
            ->assertExitCode(0);

        // Verify cleanup was successful
        $this->assertDatabaseCount('parts', 0);
        $this->assertDatabaseCount('model_parts', 0);
        $this->assertEquals(0, UserFavorite::where('favoritable_type', 'App\Models\Part')->count());
    }

    /** @test */
    public function it_maintains_referential_integrity_during_cleanup()
    {
        // Verify initial state has relationships
        $this->assertDatabaseCount('model_parts', 3);
        
        $this->artisan('parts:clean-all --force');

        // Verify all related data is properly cleaned
        $this->assertDatabaseCount('parts', 0);
        $this->assertDatabaseCount('model_parts', 0);
        
        // Verify other tables are not affected
        $this->assertDatabaseCount('categories', 3);
        $this->assertDatabaseCount('models', 3);
        $this->assertDatabaseCount('users', 2);
    }

    /** @test */
    public function it_preserves_non_part_user_favorites()
    {
        // Create a user favorite for a model (not a part)
        $user = User::first();
        $model = MobileModel::first();
        
        UserFavorite::create([
            'user_id' => $user->id,
            'favoritable_type' => 'App\Models\MobileModel',
            'favoritable_id' => $model->id,
        ]);

        $initialModelFavorites = UserFavorite::where('favoritable_type', 'App\Models\MobileModel')->count();
        $this->assertEquals(1, $initialModelFavorites);

        $this->artisan('parts:clean-all --force');

        // Verify part favorites are deleted but model favorites remain
        $this->assertEquals(0, UserFavorite::where('favoritable_type', 'App\Models\Part')->count());
        $this->assertEquals(1, UserFavorite::where('favoritable_type', 'App\Models\MobileModel')->count());
    }

    /**
     * Create test data for the tests.
     */
    private function createTestData(): void
    {
        // Create categories
        $categories = Category::factory(3)->create();

        // Create brands and models
        $brand = Brand::factory()->create();
        $models = MobileModel::factory(3)->create(['brand_id' => $brand->id]);

        // Create parts
        $parts = Part::factory(3)->create(['category_id' => $categories->first()->id]);

        // Create model-part relationships
        foreach ($parts as $index => $part) {
            $part->models()->attach($models[$index]->id, [
                'compatibility_notes' => 'Test compatibility',
                'is_verified' => true,
            ]);
        }

        // Create users and user favorites
        $users = User::factory(2)->create();
        
        UserFavorite::create([
            'user_id' => $users[0]->id,
            'favoritable_type' => 'App\Models\Part',
            'favoritable_id' => $parts[0]->id,
        ]);

        UserFavorite::create([
            'user_id' => $users[1]->id,
            'favoritable_type' => 'App\Models\Part',
            'favoritable_id' => $parts[1]->id,
        ]);
    }

    /**
     * Get current AUTO_INCREMENT value for parts table.
     */
    private function getCurrentAutoIncrement(): int
    {
        $driver = DB::getDriverName();

        if ($driver === 'mysql') {
            $result = DB::select('SELECT AUTO_INCREMENT FROM information_schema.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = "parts"');
            return $result[0]->AUTO_INCREMENT ?? 1;
        } elseif ($driver === 'sqlite') {
            // For SQLite, get the next sequence value
            $result = DB::select('SELECT seq FROM sqlite_sequence WHERE name = "parts"');
            return isset($result[0]) ? $result[0]->seq + 1 : 1;
        } else {
            // For other databases, return a default value
            return 1;
        }
    }
}
