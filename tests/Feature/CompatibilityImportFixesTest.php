<?php

namespace Tests\Feature;

use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use App\Models\SiteSetting;
use App\Models\User;
use App\Services\CompatibilityColumnService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class CompatibilityImportFixesTest extends TestCase
{
    use RefreshDatabase;

    protected $admin;
    protected $user;
    protected $part;
    protected $brand;
    protected $model;
    protected $category;
    protected $columnService;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test users
        $this->admin = User::factory()->create(['is_admin' => true]);
        $this->user = User::factory()->create(['is_admin' => false]);

        // Create test data
        $this->brand = Brand::factory()->create(['name' => 'Apple']);
        $this->category = Category::factory()->create(['name' => 'Display']);
        $this->model = MobileModel::factory()->create([
            'name' => 'iPhone 12',
            'brand_id' => $this->brand->id
        ]);
        $this->part = Part::factory()->create([
            'name' => 'LCD Screen',
            'category_id' => $this->category->id
        ]);

        $this->columnService = new CompatibilityColumnService();

        // Enable export/import functionality
        SiteSetting::set('admin_parts_export_import_enabled', true);
        
        // Clear cache
        Cache::flush();
    }

    /** @test */
    public function csv_import_only_auto_enables_columns_with_meaningful_data()
    {
        // Create CSV content with some columns having meaningful data and others empty
        $csvContent = "Brand,Model,Compatible,Display Type,Display Size,Location,Battery mAh\n";
        $csvContent .= "Apple,iPhone 12,true,OLED,6.1 inches,-,-\n";
        $csvContent .= "Apple,iPhone 12 Pro,true,OLED,6.1 inches,-,-\n";

        $file = UploadedFile::fake()->createWithContent('compatibility.csv', $csvContent);

        // Initially disable all optional columns
        $config = $this->columnService->getColumnConfiguration(true);
        $config['display_type']['enabled'] = false;
        $config['display_size']['enabled'] = false;
        $config['location']['enabled'] = false;
        $config['battery_mah']['enabled'] = false;
        $this->columnService->updateColumnConfiguration($config);

        $response = $this->actingAs($this->admin)
            ->post(route('admin.parts.import-compatibility', $this->part->id), [
                'file' => $file,
                'selected_columns' => ['Brand', 'Model', 'Compatible', 'Display Type', 'Display Size', 'Location', 'Battery mAh']
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Check that only columns with meaningful data were auto-enabled
        $updatedConfig = $this->columnService->getColumnConfiguration(true);
        
        // These should be enabled because they have meaningful data
        $this->assertTrue($updatedConfig['display_type']['enabled']);
        $this->assertTrue($updatedConfig['display_size']['enabled']);
        
        // These should remain disabled because they only have "-" values
        $this->assertFalse($updatedConfig['location']['enabled']);
        $this->assertFalse($updatedConfig['battery_mah']['enabled']);
    }

    /** @test */
    public function disabled_columns_with_meaningful_data_get_auto_enabled_during_import()
    {
        // Create CSV with meaningful data for disabled columns
        $csvContent = "Brand,Model,Compatible,Display Type,Location\n";
        $csvContent .= "Apple,iPhone 12,true,OLED,Front\n";

        $file = UploadedFile::fake()->createWithContent('compatibility.csv', $csvContent);

        // Disable the location column
        $config = $this->columnService->getColumnConfiguration(true);
        $config['location']['enabled'] = false;
        $this->columnService->updateColumnConfiguration($config);

        $response = $this->actingAs($this->admin)
            ->post(route('admin.parts.import-compatibility', $this->part->id), [
                'file' => $file,
                'selected_columns' => ['Brand', 'Model', 'Compatible', 'Display Type', 'Location']
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Check that data was imported correctly
        $this->part->load('models');
        $compatibility = $this->part->models()->where('models.id', $this->model->id)->first();

        $this->assertNotNull($compatibility);
        $this->assertEquals('Front', $compatibility->pivot->location);
        $this->assertEquals('OLED', $compatibility->pivot->display_type);

        // Check that columns with meaningful data were auto-enabled
        $response = $this->actingAs($this->admin)
            ->get(route('admin.parts.show', $this->part->id));

        $response->assertOk();
        $response->assertInertia(fn ($page) =>
            $page->has('compatibilityColumns.location')
                ->where('compatibilityColumns.location.enabled', true) // Should be auto-enabled
        );
    }

    /** @test */
    public function disclaimer_shows_to_all_users_except_admins()
    {
        // Enable disclaimer
        SiteSetting::set('parts_compatibility_disclaimer_enabled', true);
        SiteSetting::set('parts_compatibility_disclaimer_text', 'Test disclaimer text');

        // Add compatibility data to trigger disclaimer
        $this->part->models()->attach($this->model->id, [
            'is_compatible' => true,
            'is_verified' => true
        ]);

        // Test admin user - should NOT see disclaimer
        $response = $this->actingAs($this->admin)
            ->get(route('parts.show', $this->part->slug ?: $this->part->id));

        $response->assertOk();
        $response->assertInertia(fn ($page) => 
            $page->missing('disclaimerSettings')
        );

        // Test regular user - should see disclaimer
        $response = $this->actingAs($this->user)
            ->get(route('parts.show', $this->part->slug ?: $this->part->id));

        $response->assertOk();
        $response->assertInertia(fn ($page) => 
            $page->has('disclaimerSettings')
                ->where('disclaimerSettings.text', 'Test disclaimer text')
        );

        // Test guest user - should see disclaimer
        $response = $this->get(route('parts.show', $this->part->slug ?: $this->part->id));

        $response->assertOk();
        $response->assertInertia(fn ($page) => 
            $page->has('disclaimerSettings')
                ->where('disclaimerSettings.text', 'Test disclaimer text')
        );
    }

    /** @test */
    public function disabled_columns_are_hidden_in_public_view()
    {
        // Add compatibility data
        $this->part->models()->attach($this->model->id, [
            'is_compatible' => true,
            'display_type' => 'OLED',
            'location' => 'Front'
        ]);

        // Enable display_type column and disable location column
        $config = $this->columnService->getColumnConfiguration(true);
        $config['display_type']['enabled'] = true;
        $config['location']['enabled'] = false;
        $this->columnService->updateColumnConfiguration($config);

        // Test public view - disabled columns should not be included
        $response = $this->get(route('parts.show', $this->part->slug ?: $this->part->id));

        $response->assertOk();
        $response->assertInertia(fn ($page) =>
            $page->has('compatibilityColumns.display_type') // Enabled column should be present
                ->missing('compatibilityColumns.location') // Disabled column should be missing
        );
    }
}
