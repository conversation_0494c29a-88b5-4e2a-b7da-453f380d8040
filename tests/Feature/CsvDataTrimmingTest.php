<?php

namespace Tests\Feature;

use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use App\Models\SiteSetting;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Tests\TestCase;

class CsvDataTrimmingTest extends TestCase
{
    use RefreshDatabase;

    protected $admin;
    protected $brand;
    protected $category;
    protected $part;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test users
        $this->admin = User::factory()->create(['is_admin' => true]);

        // Create test data
        $this->brand = Brand::factory()->create(['name' => 'Apple']);
        $this->category = Category::factory()->create(['name' => 'Display']);
        $this->part = Part::factory()->create([
            'name' => 'LCD Screen',
            'category_id' => $this->category->id
        ]);

        // Enable export/import functionality
        SiteSetting::set('admin_parts_export_import_enabled', true);
    }

    /** @test */
    public function brands_import_trims_leading_and_trailing_spaces()
    {
        // Create CSV with spaces around brand data
        $csvContent = "name,country,website,logo_url\n";
        $csvContent .= "  Samsung  ,  South Korea  ,  https://www.samsung.com  ,  https://example.com/logo.png  \n";
        $csvContent .= " Google ,United States, https://www.google.com, \n";

        $file = UploadedFile::fake()->createWithContent('brands.csv', $csvContent);

        $response = $this->actingAs($this->admin)
            ->post(route('admin.bulk-import.brands'), [
                'file' => $file,
                'duplicate_action' => 'skip'
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify brands were created with trimmed data
        $samsung = Brand::where('name', 'Samsung')->first();
        $google = Brand::where('name', 'Google')->first();

        $this->assertNotNull($samsung);
        $this->assertEquals('Samsung', $samsung->name);
        $this->assertEquals('South Korea', $samsung->country);
        $this->assertEquals('https://www.samsung.com', $samsung->website);
        $this->assertEquals('https://example.com/logo.png', $samsung->logo_url);

        $this->assertNotNull($google);
        $this->assertEquals('Google', $google->name);
        $this->assertEquals('United States', $google->country);
        $this->assertEquals('https://www.google.com', $google->website);
        $this->assertNull($google->logo_url);
    }

    /** @test */
    public function models_import_trims_spaces_and_prevents_duplicates()
    {
        // Create CSV with spaces around model data
        $csvContent = "Brand Name,Model Name,Model Number,Release Year,Specifications,Image URL,Status\n";
        $csvContent .= "  Apple  ,  iPhone 15 Pro  ,  A3101  ,  2023  ,  display: 6.1 inch  ,  https://example.com/iphone.jpg  ,  Active  \n";

        $file = UploadedFile::fake()->createWithContent('models.csv', $csvContent);

        // First import
        $response = $this->actingAs($this->admin)
            ->post(route('admin.bulk-import.models'), [
                'file' => $file,
                'duplicate_action' => 'skip'
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify model was created with trimmed data
        $model = MobileModel::where('name', 'iPhone 15 Pro')->first();
        $this->assertNotNull($model);
        $this->assertEquals('iPhone 15 Pro', $model->name);
        $this->assertEquals('A3101', $model->model_number);
        $this->assertEquals(2023, $model->release_year);

        // Second import with different spacing - should be detected as duplicate
        $csvContent2 = "Brand Name,Model Name,Model Number,Release Year,Specifications,Image URL,Status\n";
        $csvContent2 .= "Apple,iPhone 15 Pro ,A3101,2023,display: 6.1 inch,https://example.com/iphone.jpg,Active\n";

        $file2 = UploadedFile::fake()->createWithContent('models2.csv', $csvContent2);

        $response2 = $this->actingAs($this->admin)
            ->post(route('admin.bulk-import.models'), [
                'file' => $file2,
                'duplicate_action' => 'skip'
            ]);

        $response2->assertRedirect();
        $response2->assertSessionHas('success');

        // Verify only one model exists (duplicate was skipped)
        $modelCount = MobileModel::where('name', 'iPhone 15 Pro')->count();
        $this->assertEquals(1, $modelCount);
    }

    /** @test */
    public function parts_compatibility_import_trims_spaces()
    {
        // Create a model for testing
        $model = MobileModel::factory()->create([
            'name' => 'iPhone 12',
            'brand_id' => $this->brand->id
        ]);

        // Create CSV with spaces around compatibility data
        $csvContent = "Brand,Model,Compatible,Display Type,Location\n";
        $csvContent .= "  Apple  ,  iPhone 12  ,  true  ,  OLED  ,  Front  \n";

        $file = UploadedFile::fake()->createWithContent('compatibility.csv', $csvContent);

        $response = $this->actingAs($this->admin)
            ->post(route('admin.parts.import-compatibility', $this->part->id), [
                'file' => $file,
                'selected_columns' => ['Brand', 'Model', 'Compatible', 'Display Type', 'Location']
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify compatibility was created with trimmed data
        $this->part->load('models');
        $compatibility = $this->part->models()->where('models.id', $model->id)->first();
        
        $this->assertNotNull($compatibility);
        $this->assertEquals('OLED', $compatibility->pivot->display_type);
        $this->assertEquals('Front', $compatibility->pivot->location);
    }

    /** @test */
    public function parts_import_trims_spaces()
    {
        // Create CSV with spaces around part data
        $csvContent = "Category,Part Name,Part Number,Manufacturer,Description,Status,Brand,Models\n";
        $csvContent .= "  Display  ,  LCD Screen Pro  ,  LCD-001  ,  Samsung  ,  High quality LCD  ,  Active  ,  Apple  ,  iPhone 12  \n";

        $file = UploadedFile::fake()->createWithContent('parts.csv', $csvContent);

        $response = $this->actingAs($this->admin)
            ->post(route('admin.bulk-import.parts'), [
                'file' => $file,
                'duplicate_action' => 'skip'
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify part was created with trimmed data
        $part = Part::where('name', 'LCD Screen Pro')->first();
        $this->assertNotNull($part);
        $this->assertEquals('LCD Screen Pro', $part->name);
        $this->assertEquals('LCD-001', $part->part_number);
        $this->assertEquals('Samsung', $part->manufacturer);
        $this->assertEquals('High quality LCD', $part->description);
    }

    /** @test */
    public function trimming_preserves_internal_spaces()
    {
        // Create CSV with internal spaces that should be preserved
        $csvContent = "name,country,website,logo_url\n";
        $csvContent .= "  Apple Inc  ,  United States of America  ,  https://www.apple.com  ,  \n";

        $file = UploadedFile::fake()->createWithContent('brands.csv', $csvContent);

        $response = $this->actingAs($this->admin)
            ->post(route('admin.bulk-import.brands'), [
                'file' => $file,
                'duplicate_action' => 'skip'
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify internal spaces are preserved
        $brand = Brand::where('name', 'Apple Inc')->first();
        $this->assertNotNull($brand);
        $this->assertEquals('Apple Inc', $brand->name); // Space between Apple and Inc preserved
        $this->assertEquals('United States of America', $brand->country); // Spaces preserved
    }

    /** @test */
    public function empty_and_null_values_are_handled_correctly()
    {
        // Create CSV with empty and null-like values
        $csvContent = "name,country,website,logo_url\n";
        $csvContent .= "TestBrand,,  ,\n";
        $csvContent .= "  AnotherBrand  ,  ,https://example.com,  \n";

        $file = UploadedFile::fake()->createWithContent('brands.csv', $csvContent);

        $response = $this->actingAs($this->admin)
            ->post(route('admin.bulk-import.brands'), [
                'file' => $file,
                'duplicate_action' => 'skip'
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify empty values are handled correctly
        $testBrand = Brand::where('name', 'TestBrand')->first();
        $this->assertNotNull($testBrand);
        $this->assertEquals('TestBrand', $testBrand->name);
        $this->assertNull($testBrand->country);
        $this->assertNull($testBrand->website);
        $this->assertNull($testBrand->logo_url);

        $anotherBrand = Brand::where('name', 'AnotherBrand')->first();
        $this->assertNotNull($anotherBrand);
        $this->assertEquals('AnotherBrand', $anotherBrand->name);
        $this->assertNull($anotherBrand->country);
        $this->assertEquals('https://example.com', $anotherBrand->website);
        $this->assertNull($anotherBrand->logo_url);
    }
}
