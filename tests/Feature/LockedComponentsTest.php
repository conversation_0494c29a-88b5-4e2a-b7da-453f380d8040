<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class LockedComponentsTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function locked_favorites_page_contains_upgrade_prompts()
    {
        $user = User::factory()->create([
            'subscription_plan' => 'free'
        ]);

        $response = $this->actingAs($user)->get(route('dashboard.favorites'));

        $response->assertStatus(200);
        
        // Check that the page title indicates it's a premium feature
        $response->assertSee('Premium Feature');
        
        // Check for upgrade messaging
        $response->assertSee('Upgrade to Premium');
        $response->assertSee('Premium subscription required');
    }

    /** @test */
    public function locked_favorites_page_shows_preview_content()
    {
        $user = User::factory()->create([
            'subscription_plan' => 'free'
        ]);

        $response = $this->actingAs($user)->get(route('dashboard.favorites'));

        $response->assertStatus(200);
        
        // Check for preview content
        $response->assertSee('Favorites Collection');
        $response->assertSee('Save and organize your favorite parts');
        $response->assertSee('Unlimited favorites storage');
    }

    /** @test */
    public function premium_user_sees_actual_favorites_functionality()
    {
        $user = User::factory()->create([
            'subscription_plan' => 'premium'
        ]);

        $response = $this->actingAs($user)->get(route('dashboard.favorites'));

        $response->assertStatus(200);
        
        // Should see actual favorites page, not locked version
        $response->assertSee('My Favorites');
        $response->assertSee('saved items');
        
        // Should not see upgrade prompts
        $response->assertDontSee('Upgrade to Premium');
        $response->assertDontSee('Premium subscription required');
    }

    /** @test */
    public function locked_page_has_proper_meta_title()
    {
        $user = User::factory()->create([
            'subscription_plan' => 'free'
        ]);

        $response = $this->actingAs($user)->get(route('dashboard.favorites'));

        $response->assertStatus(200);
        $response->assertSee('My Favorites - Premium Feature', false);
    }

    /** @test */
    public function upgrade_links_point_to_subscription_plans()
    {
        $user = User::factory()->create([
            'subscription_plan' => 'free'
        ]);

        $response = $this->actingAs($user)->get(route('dashboard.favorites'));

        $response->assertStatus(200);
        
        // Check that upgrade links point to subscription plans
        $response->assertSee(route('subscription.plans'));
    }

    /** @test */
    public function locked_page_shows_pricing_information()
    {
        $user = User::factory()->create([
            'subscription_plan' => 'free'
        ]);

        $response = $this->actingAs($user)->get(route('dashboard.favorites'));

        $response->assertStatus(200);
        
        // Check for pricing information
        $response->assertSee('$19');
        $response->assertSee('month');
        $response->assertSee('Cancel anytime');
    }
}
