<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Part;
use App\Models\MobileModel;
use App\Models\UserFavorite;
use App\Models\Brand;
use App\Models\Category;
use App\Models\Subscription;
use App\Models\PricingPlan;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class LockedFavoritesTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->brand = Brand::factory()->create(['name' => 'Test Brand']);
        $this->category = Category::factory()->create(['name' => 'Test Category']);
        $this->part = Part::factory()->create(['category_id' => $this->category->id]);
        $this->mobileModel = MobileModel::factory()->create(['brand_id' => $this->brand->id]);

        // Create a premium pricing plan
        $this->premiumPlan = PricingPlan::factory()->create([
            'name' => 'premium',
            'display_name' => 'Premium',
            'price' => 19,
            'is_active' => true,
        ]);
    }

    /**
     * Create a premium user with active subscription
     */
    private function createPremiumUser(): User
    {
        $user = User::factory()->create([
            'subscription_plan' => 'premium'
        ]);

        // Create active subscription
        Subscription::factory()->create([
            'user_id' => $user->id,
            'pricing_plan_id' => $this->premiumPlan->id,
            'plan_name' => 'premium',
            'status' => 'active',
            'current_period_start' => now()->subDays(5),
            'current_period_end' => now()->addDays(25),
        ]);

        return $user;
    }

    /** @test */
    public function free_user_sees_locked_favorites_page()
    {
        $user = User::factory()->create([
            'subscription_plan' => 'free'
        ]);

        $response = $this->actingAs($user)->get(route('dashboard.favorites'));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('dashboard/favorites')
                ->has('favorites')
                ->where('isPremium', false)
                ->where('favorites.total', 0)
        );
    }

    /** @test */
    public function premium_user_sees_full_favorites_functionality()
    {
        $user = $this->createPremiumUser();

        // Create some favorites for the premium user
        UserFavorite::factory()->create([
            'user_id' => $user->id,
            'favoritable_type' => Part::class,
            'favoritable_id' => $this->part->id,
        ]);

        UserFavorite::factory()->create([
            'user_id' => $user->id,
            'favoritable_type' => MobileModel::class,
            'favoritable_id' => $this->mobileModel->id,
        ]);

        $response = $this->actingAs($user)->get(route('dashboard.favorites'));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('dashboard/favorites')
                ->has('favorites')
                ->where('isPremium', true)
                ->where('favorites.total', 2)
        );
    }

    /** @test */
    public function free_user_cannot_add_favorites()
    {
        $user = User::factory()->create([
            'subscription_plan' => 'free'
        ]);

        $response = $this->actingAs($user)->post(route('dashboard.add-favorite'), [
            'type' => 'part',
            'id' => $this->part->id,
        ]);

        // Should redirect or return error for free users
        $this->assertDatabaseMissing('user_favorites', [
            'user_id' => $user->id,
            'favoritable_type' => Part::class,
            'favoritable_id' => $this->part->id,
        ]);
    }

    /** @test */
    public function premium_user_can_add_favorites()
    {
        $user = $this->createPremiumUser();

        $response = $this->actingAs($user)->post(route('dashboard.add-favorite'), [
            'type' => 'part',
            'id' => $this->part->id,
        ]);

        $this->assertDatabaseHas('user_favorites', [
            'user_id' => $user->id,
            'favoritable_type' => Part::class,
            'favoritable_id' => $this->part->id,
        ]);
    }

    /** @test */
    public function premium_user_can_remove_favorites()
    {
        $user = $this->createPremiumUser();

        $favorite = UserFavorite::factory()->create([
            'user_id' => $user->id,
            'favoritable_type' => Part::class,
            'favoritable_id' => $this->part->id,
        ]);

        $response = $this->actingAs($user)->delete(route('dashboard.remove-favorite'), [
            'type' => 'part',
            'id' => $this->part->id,
        ]);

        $this->assertDatabaseMissing('user_favorites', [
            'id' => $favorite->id,
        ]);
    }

    /** @test */
    public function user_premium_status_is_correctly_determined()
    {
        // Test free user
        $freeUser = User::factory()->create([
            'subscription_plan' => 'free'
        ]);
        $this->assertFalse($freeUser->isPremium());

        // Test premium user
        $premiumUser = $this->createPremiumUser();
        $this->assertTrue($premiumUser->isPremium());

        // Test user with free subscription plan
        $noSubUser = User::factory()->create([
            'subscription_plan' => 'free'
        ]);
        $this->assertFalse($noSubUser->isPremium());
    }

    /** @test */
    public function favorites_page_loads_relationships_correctly_for_premium_users()
    {
        $user = $this->createPremiumUser();

        // Create favorites with relationships
        UserFavorite::factory()->create([
            'user_id' => $user->id,
            'favoritable_type' => Part::class,
            'favoritable_id' => $this->part->id,
        ]);

        $response = $this->actingAs($user)->get(route('dashboard.favorites'));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('dashboard/favorites')
                ->has('favorites.data.0.favoritable.category')
        );
    }
}
