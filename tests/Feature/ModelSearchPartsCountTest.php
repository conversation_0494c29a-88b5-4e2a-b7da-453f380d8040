<?php

namespace Tests\Feature;

use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ModelSearchPartsCountTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Brand $brand;
    private Category $category;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user with premium subscription
        $this->user = User::factory()->create([
            'subscription_plan' => 'premium',
            'status' => 'active',
            'approval_status' => 'approved',
            'search_count' => 0,
            'daily_reset' => today(),
        ]);

        // Create test brand and category
        $this->brand = Brand::factory()->create(['name' => 'Test Brand', 'is_active' => true]);
        $this->category = Category::factory()->create(['name' => 'Test Category', 'is_active' => true]);
    }

    public function test_model_search_shows_correct_compatible_parts_count()
    {
        // Create a model with mixed compatibility parts
        $model = MobileModel::factory()->create([
            'name' => 'Test Model A12',
            'brand_id' => $this->brand->id,
            'is_active' => true,
        ]);

        // Create parts with different compatibility status
        $compatiblePart1 = Part::factory()->create(['category_id' => $this->category->id, 'is_active' => true]);
        $compatiblePart2 = Part::factory()->create(['category_id' => $this->category->id, 'is_active' => true]);
        $incompatiblePart = Part::factory()->create(['category_id' => $this->category->id, 'is_active' => true]);

        // Attach parts with different compatibility status
        $model->parts()->attach($compatiblePart1->id, ['is_compatible' => true, 'is_verified' => true]);
        $model->parts()->attach($compatiblePart2->id, ['is_compatible' => true, 'is_verified' => true]);
        $model->parts()->attach($incompatiblePart->id, ['is_compatible' => false, 'is_verified' => true]);

        // Make request to model search
        $response = $this->actingAs($this->user)
            ->get('/search/model?q=A12');

        $response->assertStatus(200);

        // Check that the model data includes correct parts count
        $props = $response->viewData('page')['props'];
        $models = $props['models']['data'];
        $testModel = collect($models)->firstWhere('name', 'Test Model A12');

        $this->assertNotNull($testModel);
        $this->assertEquals(2, $testModel['parts_count']); // Only compatible parts should be counted
    }

    public function test_model_search_shows_zero_parts_count_for_model_with_no_parts()
    {
        // Create a model with no parts
        $model = MobileModel::factory()->create([
            'name' => 'Empty Model A12',
            'brand_id' => $this->brand->id,
            'is_active' => true,
        ]);

        $response = $this->actingAs($this->user)
            ->get('/search/model?q=A12');

        $response->assertStatus(200);

        $props = $response->viewData('page')['props'];
        $models = $props['models']['data'];
        $testModel = collect($models)->firstWhere('name', 'Empty Model A12');

        $this->assertNotNull($testModel);
        $this->assertEquals(0, $testModel['parts_count']);
    }

    public function test_model_search_shows_zero_parts_count_for_model_with_only_incompatible_parts()
    {
        // Create a model with only incompatible parts
        $model = MobileModel::factory()->create([
            'name' => 'Incompatible Model A12',
            'brand_id' => $this->brand->id,
            'is_active' => true,
        ]);

        $incompatiblePart1 = Part::factory()->create(['category_id' => $this->category->id, 'is_active' => true]);
        $incompatiblePart2 = Part::factory()->create(['category_id' => $this->category->id, 'is_active' => true]);

        // Attach parts as incompatible
        $model->parts()->attach($incompatiblePart1->id, ['is_compatible' => false, 'is_verified' => true]);
        $model->parts()->attach($incompatiblePart2->id, ['is_compatible' => false, 'is_verified' => true]);

        $response = $this->actingAs($this->user)
            ->get('/search/model?q=A12');

        $response->assertStatus(200);

        $props = $response->viewData('page')['props'];
        $models = $props['models']['data'];
        $testModel = collect($models)->firstWhere('name', 'Incompatible Model A12');

        $this->assertNotNull($testModel);
        $this->assertEquals(0, $testModel['parts_count']); // Should be 0 since no compatible parts
    }

    public function test_model_search_counts_only_compatible_parts()
    {
        // Create a model with many parts of mixed compatibility
        $model = MobileModel::factory()->create([
            'name' => 'Mixed Parts Model A12',
            'brand_id' => $this->brand->id,
            'is_active' => true,
        ]);

        // Create 5 compatible and 3 incompatible parts
        for ($i = 0; $i < 5; $i++) {
            $part = Part::factory()->create(['category_id' => $this->category->id, 'is_active' => true]);
            $model->parts()->attach($part->id, ['is_compatible' => true, 'is_verified' => true]);
        }

        for ($i = 0; $i < 3; $i++) {
            $part = Part::factory()->create(['category_id' => $this->category->id, 'is_active' => true]);
            $model->parts()->attach($part->id, ['is_compatible' => false, 'is_verified' => true]);
        }

        $response = $this->actingAs($this->user)
            ->get('/search/model?q=A12');

        $response->assertStatus(200);

        $props = $response->viewData('page')['props'];
        $models = $props['models']['data'];
        $testModel = collect($models)->firstWhere('name', 'Mixed Parts Model A12');

        $this->assertNotNull($testModel);
        $this->assertEquals(5, $testModel['parts_count']); // Should count only the 5 compatible parts
    }

    public function test_model_search_without_query_shows_search_interface()
    {
        $response = $this->actingAs($this->user)
            ->get('/search/model');

        $response->assertStatus(200);

        // Check that it's an Inertia response with the correct component
        $props = $response->viewData('page')['props'];
        $this->assertArrayHasKey('brands', $props);
        $this->assertArrayHasKey('releaseYears', $props);
        $this->assertArrayHasKey('isSubscribed', $props);
        $this->assertArrayHasKey('hasUnlimitedAccess', $props);
    }
}
