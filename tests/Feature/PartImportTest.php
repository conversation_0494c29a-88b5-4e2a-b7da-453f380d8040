<?php

namespace Tests\Feature;

use App\Models\Part;
use App\Models\Category;
use App\Models\Brand;
use App\Models\MobileModel;
use App\Models\SiteSetting;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class PartImportTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Enable import functionality
        SiteSetting::updateOrCreate(
            ['key' => 'admin_parts_export_import_enabled'],
            [
                'value' => true,
                'type' => 'boolean',
                'description' => 'Enable CSV export/import functionality for admin parts management',
                'category' => 'parts_management',
                'is_active' => true,
            ]
        );
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_can_import_part_data_from_enhanced_compatibility_csv()
    {
        // Create test data
        $user = User::factory()->create(['role' => 'admin']);
        $category = Category::factory()->create(['name' => 'LCD']);
        $brand = Brand::factory()->create(['name' => 'Vivo']);
        $model = MobileModel::factory()->create([
            'name' => 'Y20',
            'brand_id' => $brand->id,
            'model_number' => 'V2029'
        ]);
        
        $part = Part::factory()->create([
            'category_id' => $category->id,
            'name' => 'Lcd 6.5',
            'part_number' => null,
            'manufacturer' => null,
            'description' => null,
        ]);

        // Create CSV content with enhanced part data
        $csvContent = "ID,Brand,Model,Model Number,Parts Name,Part Number,Description,Manufacturer,Category,Compatible,Verified\n";
        $csvContent .= "{$part->id},Vivo,Y20,\"V2029, V2029_PK\",Lcd 6.5,lcd-642t47,\"Test description for LCD\",Samsung,Lcd,true,true\n";

        // Create temporary CSV file
        Storage::fake('local');
        $csvFile = UploadedFile::fake()->createWithContent('test_import.csv', $csvContent);

        // Act as admin user
        $this->actingAs($user);

        // Test the import
        $response = $this->post("/admin/parts/{$part->id}/compatibility/import", [
            'file' => $csvFile,
            'selected_columns' => ['ID', 'Brand', 'Model', 'Parts Name', 'Part Number', 'Description', 'Manufacturer', 'Compatible']
        ]);

        // Assert the response
        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify part data was updated
        $part->refresh();
        $this->assertEquals('lcd-642t47', $part->part_number);
        $this->assertEquals('Samsung', $part->manufacturer);
        $this->assertEquals('Test description for LCD', $part->description);
        $this->assertEquals('Lcd 6.5', $part->name); // Should remain the same or be updated

        // Verify compatibility was also imported
        $this->assertTrue($part->models->contains($model));
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_handles_missing_required_compatibility_fields()
    {
        // Create test data
        $user = User::factory()->create(['role' => 'admin']);
        $category = Category::factory()->create(['name' => 'LCD']);

        $part = Part::factory()->create([
            'category_id' => $category->id,
            'name' => 'Lcd 6.5',
            'part_number' => null,
            'manufacturer' => null,
            'description' => null,
        ]);

        // Create CSV with part data but missing required compatibility fields
        $csvContent = "ID,Parts Name,Part Number,Description,Manufacturer\n";
        $csvContent .= "{$part->id},Lcd 6.5,lcd-642t47,Test description,Samsung\n";

        $csvFile = UploadedFile::fake()->createWithContent('test_import.csv', $csvContent);

        // Act as admin user
        $this->actingAs($user);

        // Test the import
        $response = $this->post("/admin/parts/{$part->id}/compatibility/import", [
            'file' => $csvFile,
            'selected_columns' => ['ID', 'Parts Name', 'Part Number', 'Description', 'Manufacturer']
        ]);

        // Should fail due to missing required compatibility columns (Brand, Model, Compatible)
        $response->assertRedirect();
        $response->assertSessionHasErrors();

        // Verify part data was not updated since the import failed early
        $part->refresh();
        $this->assertNull($part->part_number);
        $this->assertNull($part->manufacturer);
        $this->assertNull($part->description);
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_only_updates_part_data_from_first_row()
    {
        // Create test data
        $user = User::factory()->create(['role' => 'admin']);
        $category = Category::factory()->create(['name' => 'LCD']);
        $brand = Brand::factory()->create(['name' => 'Vivo']);
        $model1 = MobileModel::factory()->create(['name' => 'Y20', 'brand_id' => $brand->id]);
        $model2 = MobileModel::factory()->create(['name' => 'Y21', 'brand_id' => $brand->id]);
        
        $part = Part::factory()->create([
            'category_id' => $category->id,
            'name' => 'Lcd 6.5',
            'part_number' => null,
            'manufacturer' => null,
        ]);

        // Create CSV with part data in multiple rows
        $csvContent = "ID,Brand,Model,Parts Name,Part Number,Manufacturer,Compatible\n";
        $csvContent .= "{$part->id},Vivo,Y20,Lcd 6.5,lcd-642t47,Samsung,true\n";
        $csvContent .= "{$part->id},Vivo,Y21,Different LCD,different-part-number,Different Manufacturer,true\n";

        $csvFile = UploadedFile::fake()->createWithContent('test_import.csv', $csvContent);

        // Act as admin user
        $this->actingAs($user);

        // Test the import
        $response = $this->post("/admin/parts/{$part->id}/compatibility/import", [
            'file' => $csvFile,
            'selected_columns' => ['ID', 'Brand', 'Model', 'Parts Name', 'Part Number', 'Manufacturer', 'Compatible']
        ]);

        // Assert success
        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify only first row's part data was used
        $part->refresh();
        $this->assertEquals('lcd-642t47', $part->part_number);
        $this->assertEquals('Samsung', $part->manufacturer);
        
        // Should not have data from second row
        $this->assertNotEquals('different-part-number', $part->part_number);
        $this->assertNotEquals('Different Manufacturer', $part->manufacturer);
    }
}
