<?php

namespace Tests\Feature;

use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class SearchControllerStatisticsTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user
        $this->user = User::factory()->create();
        
        // Clear cache before each test
        Cache::flush();
    }

    /** @test */
    public function search_index_page_includes_statistics()
    {
        // Create test data
        $category = Category::factory()->create(['is_active' => true]);
        $brand = Brand::factory()->create(['is_active' => true]);
        Part::factory()->count(5)->create(['is_active' => true, 'category_id' => $category->id]);
        MobileModel::factory()->count(3)->create(['is_active' => true, 'brand_id' => $brand->id]);

        // Make request to search index
        $response = $this->actingAs($this->user)->get('/search');

        $response->assertStatus(200);
        
        // Check that statistics are included in the response
        $response->assertInertia(fn ($page) => 
            $page->component('search/index')
                ->has('statistics')
                ->has('statistics.parts')
                ->has('statistics.models')
                ->has('statistics.categories')
                ->has('statistics.brands')
                ->where('statistics.parts.count', 5)
                ->where('statistics.parts.formatted', '5+')
                ->where('statistics.parts.label', 'Parts Available')
                ->where('statistics.models.count', 3)
                ->where('statistics.models.formatted', '3+')
                ->where('statistics.models.label', 'Mobile Models')
                ->where('statistics.categories.count', 1)
                ->where('statistics.categories.formatted', '1+')
                ->where('statistics.categories.label', 'Categories')
                ->where('statistics.brands.count', 1)
                ->where('statistics.brands.formatted', '1+')
                ->where('statistics.brands.label', 'Brands')
        );
    }

    /** @test */
    public function search_index_redirects_guests_to_login()
    {
        $response = $this->get('/search');

        $response->assertRedirect(route('login'));
    }

    /** @test */
    public function search_index_includes_filters()
    {
        // Create test data
        Category::factory()->count(2)->create(['is_active' => true]);
        Brand::factory()->count(2)->create(['is_active' => true]);

        $response = $this->actingAs($this->user)->get('/search');

        $response->assertStatus(200);
        
        // Check that filters are included
        $response->assertInertia(fn ($page) => 
            $page->component('search/index')
                ->has('filters')
                ->has('filters.categories')
                ->has('filters.brands')
                ->has('filters.manufacturers')
                ->has('filters.release_years')
        );
    }

    /** @test */
    public function statistics_are_cached_between_requests()
    {
        // Create test data
        $category = Category::factory()->create(['is_active' => true]);
        Part::factory()->count(3)->create(['is_active' => true, 'category_id' => $category->id]);

        // First request
        $response1 = $this->actingAs($this->user)->get('/search');
        $response1->assertStatus(200);

        // Verify cache is populated
        $this->assertTrue(Cache::has('search_statistics'));

        // Second request should use cached data
        $response2 = $this->actingAs($this->user)->get('/search');
        $response2->assertStatus(200);

        // Both responses should have the same statistics
        $response1->assertInertia(fn ($page) => 
            $page->where('statistics.parts.count', 3)
        );
        
        $response2->assertInertia(fn ($page) => 
            $page->where('statistics.parts.count', 3)
        );
    }

    /** @test */
    public function statistics_handle_empty_database()
    {
        // Make request with no data in database
        $response = $this->actingAs($this->user)->get('/search');

        $response->assertStatus(200);
        
        // Check that statistics show zero counts
        $response->assertInertia(fn ($page) => 
            $page->component('search/index')
                ->where('statistics.parts.count', 0)
                ->where('statistics.parts.formatted', '0+')
                ->where('statistics.models.count', 0)
                ->where('statistics.models.formatted', '0+')
                ->where('statistics.categories.count', 0)
                ->where('statistics.categories.formatted', '0+')
                ->where('statistics.brands.count', 0)
                ->where('statistics.brands.formatted', '0+')
        );
    }

    /** @test */
    public function statistics_only_count_active_records()
    {
        // Create active and inactive records
        $category = Category::factory()->create(['is_active' => true]);
        Category::factory()->create(['is_active' => false]);
        
        $brand = Brand::factory()->create(['is_active' => true]);
        Brand::factory()->create(['is_active' => false]);
        
        Part::factory()->count(3)->create(['is_active' => true, 'category_id' => $category->id]);
        Part::factory()->count(2)->create(['is_active' => false, 'category_id' => $category->id]);
        
        MobileModel::factory()->count(2)->create(['is_active' => true, 'brand_id' => $brand->id]);
        MobileModel::factory()->count(1)->create(['is_active' => false, 'brand_id' => $brand->id]);

        $response = $this->actingAs($this->user)->get('/search');

        $response->assertStatus(200);
        
        // Should only count active records
        $response->assertInertia(fn ($page) => 
            $page->component('search/index')
                ->where('statistics.parts.count', 3)
                ->where('statistics.models.count', 2)
                ->where('statistics.categories.count', 1)
                ->where('statistics.brands.count', 1)
        );
    }

    /** @test */
    public function statistics_format_large_numbers_correctly()
    {
        // Create a large number of records to test formatting
        $category = Category::factory()->create(['is_active' => true]);
        
        // Create 1500 parts to test K+ formatting
        Part::factory()->count(1500)->create(['is_active' => true, 'category_id' => $category->id]);

        $response = $this->actingAs($this->user)->get('/search');

        $response->assertStatus(200);
        
        // Should format as 1.5K+
        $response->assertInertia(fn ($page) => 
            $page->component('search/index')
                ->where('statistics.parts.count', 1500)
                ->where('statistics.parts.formatted', '1.5K+')
        );
    }
}
