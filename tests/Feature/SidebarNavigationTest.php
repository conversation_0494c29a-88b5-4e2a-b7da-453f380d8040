<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SidebarNavigationTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function free_user_sees_favorites_link_in_sidebar()
    {
        $user = User::factory()->create([
            'subscription_plan' => 'free'
        ]);

        $response = $this->actingAs($user)->get('/dashboard');

        $response->assertStatus(200);
        // The favorites link should be present in the sidebar for all users
        $response->assertSee('Favorites');
    }

    /** @test */
    public function premium_user_sees_favorites_link_in_sidebar()
    {
        $user = User::factory()->create([
            'subscription_plan' => 'premium'
        ]);

        $response = $this->actingAs($user)->get('/dashboard');

        $response->assertStatus(200);
        // The favorites link should be present in the sidebar for all users
        $response->assertSee('Favorites');
    }

    /** @test */
    public function guest_user_does_not_see_favorites_link()
    {
        $response = $this->get('/dashboard');

        // Should redirect to login for guests
        $response->assertRedirect('/login');
    }

    /** @test */
    public function sidebar_navigation_structure_is_correct_for_free_users()
    {
        $user = User::factory()->create([
            'subscription_plan' => 'free'
        ]);

        $response = $this->actingAs($user)->get('/dashboard');

        $response->assertStatus(200);
        
        // Check that activity section items are present
        $response->assertSee('Notifications');
        $response->assertSee('Activity Log');
        $response->assertSee('Favorites');
        $response->assertSee('Search History');
        $response->assertSee('Usage Stats');
    }

    /** @test */
    public function sidebar_navigation_structure_is_correct_for_premium_users()
    {
        $user = User::factory()->create([
            'subscription_plan' => 'premium'
        ]);

        $response = $this->actingAs($user)->get('/dashboard');

        $response->assertStatus(200);
        
        // Check that activity section items are present
        $response->assertSee('Notifications');
        $response->assertSee('Activity Log');
        $response->assertSee('Favorites');
        $response->assertSee('Search History');
        $response->assertSee('Usage Stats');
    }

    /** @test */
    public function admin_user_sees_admin_navigation()
    {
        $admin = User::factory()->create([
            'is_admin' => true,
            'role' => 'admin'
        ]);

        $response = $this->actingAs($admin)->get('/dashboard');

        $response->assertStatus(200);
        
        // Admin should see admin navigation
        $response->assertSee('Core Administration');
    }

    /** @test */
    public function content_manager_sees_content_management_navigation()
    {
        $contentManager = User::factory()->create([
            'role' => 'content_manager'
        ]);

        $response = $this->actingAs($contentManager)->get('/dashboard');

        $response->assertStatus(200);
        
        // Content manager should see content management navigation
        $response->assertSee('Content Management');
    }
}
