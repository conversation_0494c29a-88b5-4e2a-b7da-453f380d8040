<?php

namespace Tests\Feature;

use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use App\Models\SiteSetting;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class UncheckedCompatibilityTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test data
        $this->brand = Brand::factory()->create(['name' => 'Apple']);
        $this->category = Category::factory()->create(['name' => 'Screens']);
        $this->part = Part::factory()->create([
            'category_id' => $this->category->id,
            'name' => 'Test Screen',
        ]);
        $this->model = MobileModel::factory()->create([
            'brand_id' => $this->brand->id,
            'name' => 'iPhone 13',
        ]);
        $this->admin = User::factory()->admin()->create();
    }

    /** @test */
    public function it_can_create_compatible_relationship()
    {
        $this->part->models()->attach($this->model->id, [
            'is_compatible' => true,
            'is_verified' => false,
        ]);

        $this->assertDatabaseHas('model_parts', [
            'part_id' => $this->part->id,
            'model_id' => $this->model->id,
            'is_compatible' => true,
        ]);
    }

    /** @test */
    public function it_can_create_incompatible_relationship()
    {
        $this->part->models()->attach($this->model->id, [
            'is_compatible' => false,
            'is_verified' => false,
        ]);

        $this->assertDatabaseHas('model_parts', [
            'part_id' => $this->part->id,
            'model_id' => $this->model->id,
            'is_compatible' => false,
        ]);
    }

    /** @test */
    public function compatible_models_scope_filters_correctly()
    {
        // Create models with different compatibility statuses
        $compatibleModel = MobileModel::factory()->create([
            'brand_id' => $this->brand->id,
            'name' => 'iPhone 14',
        ]);
        
        $this->part->models()->attach($this->model->id, [
            'is_compatible' => false,
            'is_verified' => false,
        ]);
        
        $this->part->models()->attach($compatibleModel->id, [
            'is_compatible' => true,
            'is_verified' => true,
        ]);

        // Test the compatibleModels scope
        $compatibleModels = $this->part->compatibleModels;
        
        $this->assertCount(1, $compatibleModels);
        $this->assertEquals($compatibleModel->id, $compatibleModels->first()->id);
    }

    /** @test */
    public function compatible_parts_scope_filters_correctly()
    {
        // Create parts with different compatibility statuses
        $compatiblePart = Part::factory()->create([
            'category_id' => $this->category->id,
            'name' => 'Compatible Screen',
        ]);
        
        $this->model->parts()->attach($this->part->id, [
            'is_compatible' => false,
            'is_verified' => false,
        ]);
        
        $this->model->parts()->attach($compatiblePart->id, [
            'is_compatible' => true,
            'is_verified' => true,
        ]);

        // Test the compatibleParts scope
        $compatibleParts = $this->model->compatibleParts;
        
        $this->assertCount(1, $compatibleParts);
        $this->assertEquals($compatiblePart->id, $compatibleParts->first()->id);
    }

    /** @test */
    public function disclaimer_settings_are_created()
    {
        $this->assertDatabaseHas('site_settings', [
            'key' => 'parts_compatibility_disclaimer_enabled',
            'is_active' => true,
        ]);

        $this->assertDatabaseHas('site_settings', [
            'key' => 'parts_compatibility_disclaimer_text',
            'is_active' => true,
        ]);

        $this->assertDatabaseHas('site_settings', [
            'key' => 'parts_compatibility_disclaimer_article_title',
            'is_active' => true,
        ]);

        $this->assertDatabaseHas('site_settings', [
            'key' => 'parts_compatibility_disclaimer_article_url',
            'is_active' => true,
        ]);
    }
}
