<?php

namespace Tests\Unit;

use App\Traits\CsvDataTrimmer;
use PHPUnit\Framework\TestCase;

class CsvDataTrimmerTest extends TestCase
{
    use CsvDataTrimmer;

    /** @test */
    public function trimCsvValue_trims_leading_and_trailing_spaces()
    {
        $this->assertEquals('Apple', $this->trimCsvValue('  Apple  '));
        $this->assertEquals('Samsung', $this->trimCsvValue(' Samsung '));
        $this->assertEquals('Google', $this->trimCsvValue('Google '));
        $this->assertEquals('Microsoft', $this->trimCsvValue(' Microsoft'));
    }

    /** @test */
    public function trimCsvValue_preserves_internal_spaces()
    {
        $this->assertEquals('Apple Inc', $this->trimCsvValue('  Apple Inc  '));
        $this->assertEquals('United States', $this->trimCsvValue(' United States '));
        $this->assertEquals('iPhone 15 Pro', $this->trimCsvValue('  iPhone 15 Pro  '));
    }

    /** @test */
    public function trimCsvValue_handles_null_values()
    {
        $this->assertNull($this->trimCsvValue(null));
    }

    /** @test */
    public function trimCsvValue_handles_empty_strings()
    {
        $this->assertEquals('', $this->trimCsvValue(''));
        $this->assertEquals('', $this->trimCsvValue('   '));
    }

    /** @test */
    public function trimCsvValue_handles_non_string_values()
    {
        $this->assertEquals('123', $this->trimCsvValue(123));
        $this->assertEquals('1', $this->trimCsvValue(true));
        $this->assertEquals('', $this->trimCsvValue(false));
    }

    /** @test */
    public function trimCsvData_trims_all_array_values()
    {
        $input = [
            'name' => '  Apple  ',
            'country' => ' United States ',
            'website' => '  https://www.apple.com  ',
            'logo_url' => null,
            'description' => ''
        ];

        $expected = [
            'name' => 'Apple',
            'country' => 'United States',
            'website' => 'https://www.apple.com',
            'logo_url' => null,
            'description' => ''
        ];

        $this->assertEquals($expected, $this->trimCsvData($input));
    }

    /** @test */
    public function trimCsvFields_trims_only_specified_fields()
    {
        $input = [
            'name' => '  Apple  ',
            'country' => ' United States ',
            'website' => '  https://www.apple.com  ',
            'internal_field' => '  should not be trimmed  '
        ];

        $fieldsToTrim = ['name', 'country', 'website'];

        $expected = [
            'name' => 'Apple',
            'country' => 'United States',
            'website' => 'https://www.apple.com',
            'internal_field' => '  should not be trimmed  '
        ];

        $this->assertEquals($expected, $this->trimCsvFields($input, $fieldsToTrim));
    }

    /** @test */
    public function trimCsvFields_handles_missing_fields()
    {
        $input = [
            'name' => '  Apple  ',
            'country' => ' United States '
        ];

        $fieldsToTrim = ['name', 'country', 'website', 'logo_url'];

        $expected = [
            'name' => 'Apple',
            'country' => 'United States'
        ];

        $this->assertEquals($expected, $this->trimCsvFields($input, $fieldsToTrim));
    }

    /** @test */
    public function cleanCsvHeaders_removes_bom_and_special_characters()
    {
        $input = [
            "\xEF\xBB\xBF\"Brand Name\"",
            " Model Name ",
            "'Compatible'",
            "Display\x00Type",
            "\tLocation\r\n"
        ];

        $expected = [
            'Brand Name',
            'Model Name',
            'Compatible',
            'DisplayType',
            'Location'
        ];

        $this->assertEquals($expected, $this->cleanCsvHeaders($input));
    }

    /** @test */
    public function trimCsvValue_handles_unicode_whitespace()
    {
        $input = "\u{00A0}iPhone 15\u{2000}"; // Non-breaking space and en quad
        $expected = 'iPhone 15';

        $this->assertEquals($expected, $this->trimCsvValue($input));
    }

    /** @test */
    public function normalizeForDuplicateDetection_handles_various_cases()
    {
        $testCases = [
            [' iPhone 15 ', 'iphone 15'],
            ['iPhone  15', 'iphone 15'],
            ['IPHONE 15', 'iphone 15'],
            ["\t iPhone 15 \n", 'iphone 15'],
        ];

        foreach ($testCases as [$input, $expected]) {
            $this->assertEquals($expected, $this->normalizeForDuplicateDetection($input));
        }
    }

    /** @test */
    public function normalizeForDuplicateDetection_handles_unicode_whitespace()
    {
        $input = "\u{00A0}iPhone\u{2000}15\u{00A0}"; // Non-breaking space and en quad
        $result = $this->normalizeForDuplicateDetection($input);

        // Should be trimmed and normalized to lowercase
        $this->assertEquals('iphone 15', $result);
    }

    /** @test */
    public function parseEnhancedCsvLine_trims_fields_correctly()
    {
        $input = 'Apple," iPhone 15 ",A2846," 2023 "';
        $expected = ['Apple', 'iPhone 15', 'A2846', '2023'];

        $this->assertEquals($expected, $this->parseEnhancedCsvLine($input));
    }

    /** @test */
    public function parseEnhancedCsvLine_handles_complex_quoted_fields()
    {
        $input = '"Apple Inc."," iPhone 15, Pro Max ",A2846,"Display: 6.7 inch, 120Hz"';
        $expected = ['Apple Inc.', 'iPhone 15, Pro Max', 'A2846', 'Display: 6.7 inch, 120Hz'];

        $this->assertEquals($expected, $this->parseEnhancedCsvLine($input));
    }

    /** @test */
    public function cleanCsvHeaders_handles_various_quote_types()
    {
        $input = [
            '"Brand Name"',
            "'Model Name'",
            '`Compatible`',
            'Normal Header'
        ];

        $expected = [
            'Brand Name',
            'Model Name',
            'Compatible',
            'Normal Header'
        ];

        $this->assertEquals($expected, $this->cleanCsvHeaders($input));
    }

    /** @test */
    public function cleanCsvHeaders_handles_mixed_whitespace()
    {
        $input = [
            " \t Brand Name \r\n ",
            "\x0B Model Name \x0C",
            " Compatible\x00 "
        ];

        $expected = [
            'Brand Name',
            'Model Name',
            'Compatible'
        ];

        $this->assertEquals($expected, $this->cleanCsvHeaders($input));
    }

    /** @test */
    public function trimming_handles_complex_real_world_scenarios()
    {
        // Simulate real CSV data with various spacing issues
        $input = [
            'brand_name' => '  Apple  ',
            'name' => ' iPhone 15 Pro ',
            'model_number' => '  A3101  ',
            'release_year' => ' 2023 ',
            'specifications' => '  display: 6.1 inch; storage: 128GB  ',
            'image_url' => '  https://example.com/iphone15pro.jpg  ',
            'status' => ' Active ',
            'empty_field' => '   ',
            'null_field' => null
        ];

        $expected = [
            'brand_name' => 'Apple',
            'name' => 'iPhone 15 Pro',
            'model_number' => 'A3101',
            'release_year' => '2023',
            'specifications' => 'display: 6.1 inch; storage: 128GB',
            'image_url' => 'https://example.com/iphone15pro.jpg',
            'status' => 'Active',
            'empty_field' => '',
            'null_field' => null
        ];

        $this->assertEquals($expected, $this->trimCsvData($input));
    }
}
