<?php

namespace Tests\Unit;

use App\Models\Brand;
use App\Models\Category;
use App\Models\MobileModel;
use App\Models\Part;
use App\Services\SearchStatisticsService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class SearchStatisticsServiceTest extends TestCase
{
    use RefreshDatabase;

    private SearchStatisticsService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new SearchStatisticsService();
        
        // Clear cache before each test
        Cache::flush();
    }

    /** @test */
    public function it_can_get_parts_count()
    {
        // Create test data
        Category::factory()->create(['is_active' => true]);
        Part::factory()->count(5)->create(['is_active' => true]);
        Part::factory()->count(2)->create(['is_active' => false]);

        $count = $this->service->getPartsCount();

        $this->assertEquals(5, $count);
    }

    /** @test */
    public function it_can_get_models_count()
    {
        // Create test data
        Brand::factory()->create(['is_active' => true]);
        MobileModel::factory()->count(3)->create(['is_active' => true]);
        MobileModel::factory()->count(1)->create(['is_active' => false]);

        $count = $this->service->getModelsCount();

        $this->assertEquals(3, $count);
    }

    /** @test */
    public function it_can_get_categories_count()
    {
        // Create test data
        Category::factory()->count(4)->create(['is_active' => true]);
        Category::factory()->count(2)->create(['is_active' => false]);

        $count = $this->service->getCategoriesCount();

        $this->assertEquals(4, $count);
    }

    /** @test */
    public function it_can_get_brands_count()
    {
        // Create test data
        Brand::factory()->count(6)->create(['is_active' => true]);
        Brand::factory()->count(1)->create(['is_active' => false]);

        $count = $this->service->getBrandsCount();

        $this->assertEquals(6, $count);
    }

    /** @test */
    public function it_formats_counts_correctly()
    {
        $this->assertEquals('50+', $this->service->formatCount(50));
        $this->assertEquals('500+', $this->service->formatCount(500));
        $this->assertEquals('5.0K+', $this->service->formatCount(5000));
        $this->assertEquals('50.0K+', $this->service->formatCount(50000));
        $this->assertEquals('500.0K+', $this->service->formatCount(500000));
        $this->assertEquals('5.0M+', $this->service->formatCount(5000000));
    }

    /** @test */
    public function it_returns_formatted_statistics()
    {
        // Create test data
        $category = Category::factory()->create(['is_active' => true]);
        $brand = Brand::factory()->create(['is_active' => true]);
        Part::factory()->count(3)->create(['is_active' => true, 'category_id' => $category->id]);
        MobileModel::factory()->count(2)->create(['is_active' => true, 'brand_id' => $brand->id]);

        $stats = $this->service->getFormattedStatistics();

        $this->assertArrayHasKey('parts', $stats);
        $this->assertArrayHasKey('models', $stats);
        $this->assertArrayHasKey('categories', $stats);
        $this->assertArrayHasKey('brands', $stats);

        $this->assertEquals(3, $stats['parts']['count']);
        $this->assertEquals('3+', $stats['parts']['formatted']);
        $this->assertEquals('Parts Available', $stats['parts']['label']);

        $this->assertEquals(2, $stats['models']['count']);
        $this->assertEquals('2+', $stats['models']['formatted']);
        $this->assertEquals('Mobile Models', $stats['models']['label']);
    }

    /** @test */
    public function it_caches_statistics()
    {
        // Create test data
        Category::factory()->create(['is_active' => true]);
        Part::factory()->count(5)->create(['is_active' => true]);

        // First call should hit the database
        $firstCall = $this->service->getPartsCount();
        
        // Second call should use cache
        $secondCall = $this->service->getPartsCount();

        $this->assertEquals($firstCall, $secondCall);
        $this->assertEquals(5, $firstCall);

        // Verify cache is being used
        $this->assertTrue(Cache::has('search_stats_parts_count'));
    }

    /** @test */
    public function it_can_clear_cache()
    {
        // Create test data and populate cache
        Category::factory()->create(['is_active' => true]);
        Part::factory()->count(3)->create(['is_active' => true]);
        $this->service->getPartsCount();

        // Verify cache exists
        $this->assertTrue(Cache::has('search_stats_parts_count'));

        // Clear cache
        $this->service->clearCache();

        // Verify cache is cleared
        $this->assertFalse(Cache::has('search_stats_parts_count'));
        $this->assertFalse(Cache::has('search_statistics'));
    }

    /** @test */
    public function it_can_refresh_cache()
    {
        // Create initial data
        $category = Category::factory()->create(['is_active' => true]);
        Part::factory()->count(2)->create(['is_active' => true, 'category_id' => $category->id]);

        // Get initial statistics
        $initialStats = $this->service->getSearchStatistics();
        $this->assertEquals(2, $initialStats['parts_count']);

        // Add more data
        Part::factory()->count(3)->create(['is_active' => true, 'category_id' => $category->id]);

        // Refresh cache
        $refreshedStats = $this->service->refreshCache();

        // Verify updated statistics
        $this->assertEquals(5, $refreshedStats['parts_count']);
    }

    /** @test */
    public function it_returns_comprehensive_statistics()
    {
        // Create test data
        $category = Category::factory()->create(['is_active' => true]);
        $brand = Brand::factory()->create(['is_active' => true]);
        Part::factory()->count(3)->create(['is_active' => true, 'category_id' => $category->id]);
        MobileModel::factory()->count(2)->create(['is_active' => true, 'brand_id' => $brand->id]);

        $stats = $this->service->getSearchStatistics();

        $this->assertArrayHasKey('parts_count', $stats);
        $this->assertArrayHasKey('models_count', $stats);
        $this->assertArrayHasKey('categories_count', $stats);
        $this->assertArrayHasKey('brands_count', $stats);
        $this->assertArrayHasKey('additional_stats', $stats);

        $this->assertEquals(3, $stats['parts_count']);
        $this->assertEquals(2, $stats['models_count']);
        $this->assertEquals(1, $stats['categories_count']);
        $this->assertEquals(1, $stats['brands_count']);
    }
}
